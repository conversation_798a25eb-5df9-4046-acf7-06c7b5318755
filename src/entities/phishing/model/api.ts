import { globalBaseApi, GlobalEndpointBuilder } from '@/store/services/endpoints/base'

import {
  IAuthphishResponse,
  IAuthphishUpdateRequest,
  IAuthphishUpdateResponse,
  ICreatePhishingCampaignBody,
  IEmail,
  IPhishingCapmaign,
  IPhishingCampaign,
  IPhishingCapmaignsStatusesResponse,
  IPhishingCategoriesReponse,
  IPhishingCreateTemplatesRequest,
  IPhishingDomainsResponse,
  IPhishingTemplate,
  IPhishingTemplateWithData,
  IIPhishingCampaignStatisticsUserResponse,
  IPhishingCampaignStatisticsDepartmentResponse,
  IPhishingCampaignTemplatesResponse,
  IPhishingCampaignResult,
  RedirectPage,
  IPhishingCampaignCourse,
  ICreatePhishingCampaignByUsersParams,
  IPhishingStatisticsChart,
  Report,
  IPhishingCapmaignWithData,
  IPhishingCampaignRequest,
} from './types'
import { BACKEND_URLS } from '@/shared/constants/urls'

const PREFIX_URL = '/lk/api/v2'

const getPhishingQueries = () => {
  return (build: GlobalEndpointBuilder) => ({
    getTemplates: build.query<IPhishingTemplate[], { search?: string }>({
      query: ({ search }) => ({
        url: PREFIX_URL + `/phishing/templates${search ? `?search=${search}&` : ''}`,
      }),
      providesTags: ['templates'],
      transformResponse: (response: IPhishingTemplateWithData) => response.data,
    }),
    getPhishingCampaignsTableDepartmentsById: build.query<
      IPhishingCampaignStatisticsDepartmentResponse,
      { id: UUID; params: Record<string, number | string | boolean> }
    >({
      query: ({ id, params }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}/departments/`,
        params,
      }),
      providesTags: ['campaign'],
    }),
    getCategories: build.query<IPhishingCategoriesReponse, void>({
      query: () => ({
        url: PREFIX_URL + `/phishing/categories`,
        method: 'GET',
      }),
    }),
    getDomains: build.query<IPhishingDomainsResponse, void>({
      query: () => ({
        url: PREFIX_URL + `/phishing/domains/names`,
        method: 'GET',
      }),
    }),
    getTemplateById: build.query<IPhishingTemplate, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/templates/${id}`,
      }),
      providesTags: ['template-by-id'],
    }),
    getPageById: build.query<IEmail, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/pages/${id}`,
      }),
      providesTags: ['page-by-id'],
    }),
    getAutoPhishFinishDate: build.query<
      { campaign_end_date: string },
      { targets: number; startDate?: string }
    >({
      query: ({ startDate, targets }) => ({
        url: PREFIX_URL + `/phishing/campaigns/autoFinishDate/`,
        params: { targets, startDate },
      }),
    }),
    getTemplateStatistic: build.query<IPhishingStatisticsChart, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/templates/${id}/statistics`,
      }),
    }),
    getEmail: build.query<IEmail, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/emails/${id}`,
      }),
      providesTags: ['email'],
    }),
    getAutophish: build.query<IAuthphishResponse, void>({
      query: () => ({
        url: PREFIX_URL + `/phishing/autophish`,
      }),
      providesTags: ['autophish'],
    }),
    getPhishingCampaignsStatuses: build.query<
      IPhishingCapmaignsStatusesResponse,
      { params: { by_tag?: boolean } }
    >({
      query: ({ params }) => ({
        url: PREFIX_URL + `/phishing/campaigns/statuses`,
        params: params,
      }),
      providesTags: ['statuses'],
    }),
    getPhishingCampaigns: build.query<IPhishingCapmaignWithData, void | IPhishingCampaignRequest>({
      query: requestParams => ({
        url: PREFIX_URL + `/phishing/campaigns`,
        params: requestParams || {},
      }),
      providesTags: ['campaigns'],
    }),
    getPhishingLastCampaign: build.query<IPhishingCampaign, UUID | void>({
      query: organization_id => ({
        url: `/gateway/v1/statistic/organizations/${organization_id}/last-campaign`,
      }),
      providesTags: ['campaigns'],
    }),
    getPhishingCampaignsById: build.query<
      IPhishingCapmaign,
      { id: UUID; params: { by_tag?: boolean } }
    >({
      query: ({ id, params = { by_tag: false } }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}`,
        params,
      }),
      providesTags: ['campaign'],
    }),
    getPhishingCampaignCourseById: build.query<
      IPhishingCampaignCourse,
      { id: UUID; params: { by_tag?: boolean } }
    >({
      query: ({ id, params = { by_tag: false } }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}/course`,
        params,
      }),
      providesTags: ['campaign'],
    }),
    getPhishingCampaignEmailingTemplate: build.query<
      IPhishingCampaignResult,
      {
        campaign_id: UUID
        campaign_template_id: UUID
        params: { by_tag: boolean }
      }
    >({
      query: ({ campaign_id, campaign_template_id, params = { by_tag: false } }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${campaign_id}/templates/${campaign_template_id}`,
        method: 'GET',
        params,
      }),
    }),
    getPhishingCampaignsOsById: build.query<
      { data: string[] },
      { id: UUID; templateId: UUID; byTag: boolean }
    >({
      query: ({ id, templateId, byTag }) => {
        const params: { by_tag: boolean; template_id?: string } = { by_tag: byTag }

        if (templateId) params.template_id = templateId

        return {
          url: PREFIX_URL + `/phishing/campaigns/${id}/os/`,
          params,
        }
      },
      providesTags: ['campaign'],
    }),
    getPhishingCampaignsTableUsersById: build.query<
      IIPhishingCampaignStatisticsUserResponse,
      {
        id: UUID
        params: {
          limit?: number | null
          offset?: number | null
          search?: string
          template_id?: string
          by_tag?: boolean
          status?: UUID[]
          os?: UUID[]
        }
      }
    >({
      query: ({ id, params }) => {
        let query = `/phishing/campaigns/${id}/users?search=${encodeURIComponent(params.search || '')}`

        query += `${params.limit ? `&limit=${params.limit}` : ''}`
        query += `${params.offset ? `&offset=${params.offset}` : ''}`
        query += `${params.by_tag ? `&by_tag=${params.by_tag}` : ''}`
        query += `${params.template_id ? `&template_id=${params.template_id}` : ''}`
        query += `${params.status && params.status.length > 0 ? `&status=${params.status.join(`&status=`)}` : ''}`
        query += `${params.os && params.os.length > 0 ? `&os=${params.os.join(`&os=`)}` : ''}`

        return {
          url: PREFIX_URL + query,
        }
      },
      providesTags: ['campaign'],
    }),
    getPhishingCampaignsReport: build.query<ResponseWithNotification<Report>, UUID>({
      query: id => ({
        url: PREFIX_URL + `/reports/${id}`,
      }),
      providesTags: ['campaign-template'],
      transformResponse: (response: Report) => {
        if (response.status === 'complete' && response.url) {
          return {
            ...response,
            notificationTip: 'tips:report.success',
          }
        }

        return response
      },
    }),
    getPhishingCampaignsTemplatesById: build.query<
      IPhishingCampaignTemplatesResponse['data'],
      {
        id: UUID
        params?: {
          by_tag: boolean
        }
      }
    >({
      query: ({ id, params = { by_tag: false } }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}/templates`,
        params,
      }),
      transformResponse: (response: IPhishingCampaignTemplatesResponse) => response.data,
      providesTags: ['campaign-templates'],
    }),
    //! TAGS
    getAutophishingSettingsById: build.query<IPhishingCampaignTemplatesResponse['data'], UUID>({
      query: id => ({
        url: PREFIX_URL + `/users/tags/${id}/autophishing`,
        method: 'GET',
      }),
      transformResponse: (response: IPhishingCampaignTemplatesResponse) => response.data,
      providesTags: ['phishing-tag'],
    }),
    getRedirectPage: build.query<RedirectPage, { id: UUID }>({
      query: ({ id }) => ({
        url: PREFIX_URL + `/phishing/redirect_pages/${id}`,
      }),
      providesTags: ['redirect-pages'],
    }),
    getRedirectPages: build.query<{ data: RedirectPage[] }, void>({
      query: () => ({
        url: PREFIX_URL + `/phishing/redirect_pages`,
      }),
      providesTags: ['redirect-pages'],
    }),
  })
}

const getPhishingMutations = () => {
  return (build: GlobalEndpointBuilder) => ({
    createTemplate: build.mutation<
      ResponseWithNotification<IPhishingTemplate>,
      IPhishingCreateTemplatesRequest
    >({
      query: body => ({
        url: PREFIX_URL + `/phishing/templates`,
        method: 'POST',
        body,
      }),
      invalidatesTags: result => {
        return result?.id ? ['templates'] : []
      },
      transformResponse: (response: IPhishingTemplate) => ({
        ...response,
        notificationTip: 'tips:templates.create',
      }),
    }),
    copyTemplateById: build.mutation<ResponseWithNotification<IPhishingTemplate>, { id: string }>({
      query: ({ id }) => ({
        url: PREFIX_URL + `/phishing/templates/${id}/copy`,
        method: 'POST',
      }),
      invalidatesTags: result => {
        return result?.id ? ['templates'] : []
      },
      transformResponse: (response: IPhishingTemplate) => ({
        ...response,
        notificationTip: 'tips:templates.copy',
      }),
    }),
    deletePhishingTemplate: build.mutation<ResponseWithNotification<void>, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/templates/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['templates'],
      transformResponse: () => ({
        notificationTip: 'tips:templates.delete',
      }),
    }),
    patchPhishingTemplateById: build.mutation<
      ResponseWithNotification<IPhishingTemplate>,
      { body: IPhishingCreateTemplatesRequest; id: string }
    >({
      query: ({ body, id }) => ({
        url: PREFIX_URL + `/phishing/templates/` + id,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['template-by-id', 'templates'],
      transformResponse: (response: IPhishingTemplate) => ({
        ...response,
        notificationTip: 'tips:templates.update',
      }),
    }),
    deleteEmails: build.mutation<ResponseWithNotification<void>, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/emails/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['emails'],
      transformResponse: () => ({
        notificationTip: 'tips:templates.delete',
      }),
    }),
    createEmail: build.mutation<ResponseWithNotification<IEmail>, Omit<IEmail, 'id'>>({
      query: body => ({
        url: PREFIX_URL + `/phishing/emails`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['email', 'template-by-id'],
      transformResponse: (response: IEmail) => ({
        ...response,
        notificationTip: 'tips:mail.success',
      }),
    }),
    patchEmail: build.mutation<ResponseWithNotification<IEmail>, IEmail>({
      query: body => ({
        url: PREFIX_URL + `/phishing/emails/${body.id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['email', 'template-by-id'],
      transformResponse: (response: IEmail) => ({
        ...response,
        notificationTip: 'tips:mail.update',
      }),
    }),
    createPageById: build.mutation<IEmail, Pick<IEmail, 'html'> & { template_id: string }>({
      query: body => ({
        url: PREFIX_URL + `/phishing/pages`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['page-by-id'],
      transformResponse: (response: IEmail) => ({
        ...response,
        notificationTip: 'tips:pages.create',
      }),
    }),
    patchPageById: build.mutation<IEmail, IEmail>({
      query: body => ({
        url: PREFIX_URL + `/phishing/pages/${body.id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['page-by-id'],
      transformResponse: (response: IEmail) => ({
        ...response,
        notificationTip: 'tips:pages.update',
      }),
    }),
    updateAutophish: build.mutation<
      ResponseWithNotification<IAuthphishUpdateResponse>,
      IAuthphishUpdateRequest
    >({
      query: body => ({
        url: PREFIX_URL + `/phishing/autophish`,
        method: 'POST',
        params: { toggle: body.enabled },
      }),
      invalidatesTags: ['autophish'],
      transformResponse: (response: IAuthphishUpdateResponse, _, { enabled }) => ({
        ...response,
        notification: `tips:phishing.${enabled ? 'on' : 'off'}`,
      }),
    }),
    //! Phishing

    createPhishingCampaign: build.mutation<
      ResponseWithNotification<IPhishingCapmaign>,
      ICreatePhishingCampaignBody
    >({
      query: body => ({
        url: PREFIX_URL + `/phishing/campaigns`,
        body,
        method: 'POST',
      }),
      invalidatesTags: ['campaigns'],
      transformResponse: (response: IPhishingCapmaign) => ({
        ...response,
        notificationTip: 'tips:campaigns.create',
      }),
    }),
    createPhishingCampaignByFilteredUsers: build.mutation<
      ResponseWithNotification<IPhishingCapmaign>,
      {
        body: Omit<ICreatePhishingCampaignBody, 'targets'>
        params: ICreatePhishingCampaignByUsersParams
      }
    >({
      query: ({ body, params }) => {
        const searchParams = new URLSearchParams()

        searchParams.append('need_all', params.need_all.toString())
        if (params.search) searchParams.append('search', params.search)
        if (params.roles !== undefined && params.roles !== null)
          searchParams.append('roles', params.roles.toString())
        searchParams.append('risk_level_from', params.risk_level_from.toString())
        searchParams.append('risk_level_to', params.risk_level_to.toString())

        const arrayFields: (keyof ICreatePhishingCampaignByUsersParams)[] = [
          'in_phishing',
          'phishing_events',
          'position',
          'departments',
          'tags',
          'in_course',
          'learning',
          'course_progress',
          'include_ids',
          'exclude_ids',
        ]

        arrayFields.forEach(field => {
          const value = params[field]
          if (Array.isArray(value)) {
            value.forEach(item => {
              if (item !== undefined && item !== null) {
                searchParams.append(field as string, item.toString())
              }
            })
          }
        })

        return {
          url: PREFIX_URL + `/phishing/campaigns/filtered`,
          method: 'POST',
          body,
          params: searchParams,
        }
      },
      transformResponse: (response: IPhishingCapmaign) => ({
        ...response,
        notificationTip: 'tips:campaigns.create',
      }),
    }),
    completePhishingCampaignById: build.mutation<ResponseWithNotification<void>, UUID | null>({
      query: id => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}/complete`,
        method: 'POST',
      }),
      invalidatesTags: ['campaign', 'campaigns', 'statuses'],
      transformResponse: () => ({
        notificationTip: 'tips:campaigns.end',
      }),
    }),
    deletePhishingCampaignsById: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['campaigns', 'statuses'],
      transformResponse: () => ({
        notificationTip: 'tips:campaigns.delete',
      }),
    }),
    createPhishingCampaignReport: build.mutation<
      Report,
      {
        campaign_id: string
      }
    >({
      query: body => ({
        url: PREFIX_URL + `/reports/phishing-campaign`,
        body,
        method: 'POST',
      }),
      invalidatesTags: ['campaign-template'],
    }),

    getPhishingCampaignsTableDepartmentsById: build.query<
      IPhishingCampaignStatisticsDepartmentResponse,
      { id: UUID; params: Record<string, number | string | boolean> }
    >({
      query: ({ id, params }) => ({
        url: PREFIX_URL + `/phishing/campaigns/${id}/departments/`,
        params,
      }),
      providesTags: ['campaign'],
    }),

    createAuthophishingTemplatesByTagId: build.mutation<
      { id: UUID },
      { id: UUID; ids: UUID[]; defer_by?: number }
    >({
      query: ({ id, ids, defer_by }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${id}/phishing`,
        body: {
          templates_ids: ids,
          defer_by,
        },
        method: 'POST',
      }),
      transformResponse: (response: { id: UUID }) => ({
        ...response,
        notificationTip: 'tips:phishing.save_action',
      }),
      // invalidatesTags: ["phishing-tag"],
    }),
    updateAuthophishingTemplatesByTagId: build.mutation<{ id: UUID }, { id: UUID; ids: UUID[] }>({
      query: ({ id, ids }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${id}/phishing/templates`,
        body: {
          templates_ids: ids,
        },
        method: 'PATCH',
      }),
      invalidatesTags: ['phishing-tag'],
    }),
    updateAuthophishingDeferByTagId: build.mutation<{ id: UUID }, { id: UUID; defer_by?: number }>({
      query: ({ id, defer_by }) => ({
        url:
          BACKEND_URLS.USER_SERVICE +
          `/tags/${id}/phishing/defer-by?${defer_by !== undefined ? `defer_by=${defer_by}` : ''}`,
        method: 'PATCH',
      }),
      invalidatesTags: ['phishing-tag'],
    }),
    deleteAuthophishingTemplatesByTagId: build.mutation<{ id: UUID }, UUID>({
      query: id => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/${id}/phishing`,
        method: 'DELETE',
      }),
      invalidatesTags: ['phishing-tag'],
      transformResponse: (response: { id: UUID }) => ({
        ...response,
        notificationTip: 'tips:campaigns.delete',
      }),
    }),

    createRedirectPage: build.mutation<RedirectPage, RedirectPage>({
      query: body => ({
        url: PREFIX_URL + `/phishing/redirect_pages`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['redirect-pages'],
      transformResponse: (response: RedirectPage) => ({
        ...response,
        notificationTip: 'tips:pages.redirect.create',
      }),
    }),
    deleteRedirectPage: build.mutation<RedirectPage, { id: UUID }>({
      query: ({ id }) => ({
        url: PREFIX_URL + `/phishing/redirect_pages/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['redirect-pages'],
      transformResponse: (response: RedirectPage) => ({
        ...response,
        notificationTip: 'tips:pages.redirect.delete',
      }),
    }),
    editRedirectPage: build.mutation<RedirectPage, { id: UUID; data: RedirectPage }>({
      query: ({ id, data }) => ({
        url: PREFIX_URL + `/phishing/redirect_pages/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['redirect-pages'],
      transformResponse: (response: RedirectPage) => ({
        ...response,
        notificationTip: 'tips:pages.redirect.update',
      }),
    }),
  })
}

export const phishingQueries = globalBaseApi.injectEndpoints({
  endpoints: builder => getPhishingQueries()(builder),
  overrideExisting: true,
})

export const phishingMutations = globalBaseApi.injectEndpoints({
  endpoints: builder => getPhishingMutations()(builder),
  overrideExisting: true,
})
