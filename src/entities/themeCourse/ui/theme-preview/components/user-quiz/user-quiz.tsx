import { UserQuizProps } from './user-quiz.d'
import type { Answers } from './user-quiz.d'
import type { TQuestion } from './types'
import React, { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './user-quiz.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectDisableFetch,
  selectSectionId,
  selectTheme,
  setDisableNext,
} from '@/store/slices/user-course-slice'
import { userStaticticsApi } from '@/pages/user/learning/endpoints'
import { useNavigate, useParams } from 'react-router-dom'
import { UserTestHeader } from '@/shared/components/user-test-header'
import { useTranslation } from 'react-i18next'
import { Button, Loader } from '@/shared/ui'
import { userQuizApi } from './endpoints'
import { QuizResponse } from './types'
import { TestCard } from '@/shared/components/test-card'
import { UserTestHeaderView } from '@/shared/components/user-test-header-view'
import { TestCardView } from '@/shared/components/test-card-view'
import { testApi } from '@/entities/courses/model/api/endpoints'
import { TestTimeOverModal } from '@/shared/modals/test-time-over-modal'
import { themeStepsApi } from '@/entities/themeCourse/model/api'
import { AttemptsLimitModal } from '@/shared/modals/attempts-limit-modal'
import { URLS } from '@/shared/configs/urls'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

// Constants
const TRANSLATION_FILE = 'components__user-quiz'
const TIMER_OFFSET_MS = 5000 // 5 seconds before end
const UI_DELAY_MS = 500
const ERROR_MESSAGES = {
  time_over: 'This attempt already finished',
} as const

const Quiz: FC<UserQuizProps.Props> = memo(
  ({
    isStarted,
    stepId,
    progressId,
    isQuizStarted,
    setProgressId,
    courseId,
    sectionId,
    themeId,
    currentData,
    isLoading,
    newAttempt,
    setIsStarted,
    setCurrentData,
    refetch,
    getState,
    setForceShowResult,
  }) => {
    const { t } = useTranslation(TRANSLATION_FILE)
    const navigate = useNavigate()
    const [answers, setAnswers] = useState<Answers>({})
    const [showTimeOverModal, setShowTimeOverModal] = useState<boolean>(false)
    const attemptStarted = useRef<boolean>(false)
    const timeIsOver = useRef<boolean>(false)
    const answersRef = useRef<Answers>(answers)
    const timerSendAnswerRef = useRef<NodeJS.Timeout | null>(null)
    const timerFinishRef = useRef<NodeJS.Timeout | null>(null)
    const answersAlreadySent = useRef<boolean>(false)
    const analytics = useAnalytics()
    const [answerQuestion, { isLoading: isAnswerQuestionLoading }] =
      userQuizApi.useAnswerQuestionMutation()
    const [completeQuiz, { isLoading: isCompleteQuizLoading }] =
      userQuizApi.useCompleteQuizMutation()
    const [initStep] = testApi.useInitStepsMutation()

    const [isCustomLoading, setIsCustomLoading] = useState(false)
    const isDisabled =
      isAnswerQuestionLoading || isCompleteQuizLoading || isLoading || isCustomLoading

    const handleCancel = () => {
      analytics.event(GOALS['course-cancel-quiz'].name)
      navigate(URLS.USER_MY_COURSES_PAGE)
    }

    const handleStart = async () => {
      if (!courseId || !sectionId || !stepId || !themeId) return
      answersAlreadySent.current = false
      if (!isQuizStarted) {
        await initStep({
          assigned_course_id: courseId,
          body: {
            step_id: stepId,
            section_id: sectionId,
            theme_id: themeId,
          },
        })
          .unwrap()
          .then(res => {
            analytics.event(GOALS['course-start-quiz'].name)
            setProgressId(res.id)
            setCurrentData(res as QuizResponse)
            setIsStarted(true)
            refetch()
          })
      } else {
        newAttempt().then(() => refetch())
      }
    }

    const handleSubmit = async () => {
      analytics.event(GOALS['course-repeat-quiz'].name)
      answersAlreadySent.current = false
      await newAttempt().then(() => refetch())
      await setAnswers({})
      await setShowTimeOverModal(false)
    }

    const handleAnswer = (questionId: UUID, answerId: UUID, hasManyAnswers: boolean) => {
      analytics.event(GOALS['course-answer-quiz'].name, { questionId, answerId, hasManyAnswers })

      if (hasManyAnswers && questionId in answers) {
        const index = answers[questionId].indexOf(answerId)
        const res = [...answers[questionId]]
        if (index === -1) {
          res.push(answerId)
        } else {
          res.splice(index, 1)
        }
        setAnswers(prev => ({
          ...prev,
          [questionId]: res,
        }))
      } else {
        setAnswers(prev => ({
          ...prev,
          [questionId]: [answerId],
        }))
      }
    }

    const handleFinish = useCallback(async () => {
      if (isDisabled) return

      setIsCustomLoading(true)
      if (
        !currentData?.attempts[currentData?.attempts.length - 1].id ||
        !themeId ||
        !progressId ||
        !stepId
      ) {
        return
      }
      try {
        clearTimers()

        await handleSendAnswer()

        const res = await completeQuiz({
          assigned_course_id: courseId,
          body: {
            section_id: sectionId,
            step_id: stepId,
            step_progress_id: progressId,
            theme_id: themeId,
            attempt_id: currentData?.attempts[currentData?.attempts.length - 1].id,
          },
        }).unwrap()

        analytics.event(GOALS['course-finish-quiz'].name)
        setCurrentData(res as QuizResponse)
        refetch()
        getState()

        if (timeIsOver.current && setForceShowResult) {
          setForceShowResult(true)
        }

        setTimeout(() => setIsCustomLoading(false), UI_DELAY_MS)
      } catch (e) {
        //@ts-expect-error add type for all errors in catch
        if (e.data.detail === ERROR_MESSAGES.time_over) {
          setShowTimeOverModal(true)
        }
        setIsCustomLoading(false)
      } finally {
        setTimeout(() => {
          const target = document.querySelector('#page-wrapper')
          target?.scrollTo({ top: 0, behavior: 'smooth' })
        }, 0)
      }
    }, [currentData])

    const handleSendAnswer = useCallback(async () => {
      const currentAnswers = answersRef.current
      if (
        !currentData?.attempts[currentData?.attempts.length - 1].id ||
        !themeId ||
        !progressId ||
        !stepId ||
        answersAlreadySent.current
      ) {
        return
      }

      const questions = []
      for (const question in currentAnswers) {
        questions.push({
          question_id: question,
          answers_ids: currentAnswers[question],
        })
      }

      if (questions.length === 0) return

      await answerQuestion({
        assigned_course_id: courseId,
        body: {
          section_id: sectionId,
          step_id: stepId,
          step_progress_id: progressId,
          theme_id: themeId,
          attempt_id: currentData?.attempts[currentData?.attempts.length - 1].id,
          questions: questions,
        },
      }).unwrap()

      answersAlreadySent.current = true
    }, [currentData])

    const clearTimers = () => {
      if (timerSendAnswerRef.current) clearTimeout(timerSendAnswerRef.current)
      if (timerFinishRef.current) clearTimeout(timerFinishRef.current)
    }

    useEffect(() => {
      answersRef.current = answers
    }, [answers])

    useEffect(() => () => clearTimers(), [])

    useEffect(() => {
      if (!currentData || !currentData.settings.testing_time_duration || !isStarted) return
      if (attemptStarted.current) return

      attemptStarted.current = true
      answersAlreadySent.current = false
      const startTime = new Date(
        currentData.attempts[currentData.attempts.length - 1].started_at,
      ).getTime()
      const duration = currentData.settings.testing_time_duration * 60 * 1000 // mins in milliseconds
      const endTime = startTime + duration
      const timeLeft = endTime - Date.now()

      if (timeLeft <= 0) {
        timeIsOver.current = true
        handleFinish()
        return
      }

      timerSendAnswerRef.current = setTimeout(handleSendAnswer, timeLeft - TIMER_OFFSET_MS)
      timerFinishRef.current = setTimeout(() => {
        timeIsOver.current = true
        handleFinish()
      }, timeLeft)
    }, [currentData, handleFinish, handleSendAnswer, isStarted])

    if (!isStarted) {
      return (
        <div className={cx('start')}>
          <div className={cx('welcomeText')}>{t('welcome_text')}</div>
          <div className={cx('buttons')}>
            <Button onClick={handleCancel} color='darkGray' size='big'>
              {t('cancel')}
            </Button>
            <Button onClick={handleStart} color='green' size='big'>
              {t('start')}
            </Button>
          </div>
        </div>
      )
    }

    if (isLoading || !currentData) {
      return <Loader size='56' className={cx('loader')} />
    }

    const attempts = currentData.settings.attempts_limit
      ? currentData.settings.attempts_limit - currentData.attempts.length
      : undefined

    const isAnswersFilled =
      Object.keys(answers).length ===
        currentData.attempts[currentData.attempts.length - 1].questions.length &&
      Object.keys(answers).every(a => answers[a].length > 0)

    return (
      <div
        className={cx('quizWrapper')}
        key={currentData.attempts[currentData.attempts.length - 1].id}
      >
        <UserTestHeader
          finished={!!currentData.finished_at}
          isLimit={!!currentData.settings.attempts_limit}
          attempts={attempts}
          start_date={currentData.attempts[currentData.attempts.length - 1].started_at}
          duration={currentData.settings?.testing_time_duration}
        />
        <div className={cx('cardsWrapper')}>
          {currentData.attempts[currentData.attempts.length - 1].questions.map(
            (question: TQuestion, index: number) => (
              <TestCard
                question={question}
                key={question.id}
                answers={answers[question.id]}
                order={index + 1}
                onChangeAnswer={handleAnswer}
              />
            ),
          )}
        </div>
        <Button
          onClick={handleFinish}
          disabled={!isAnswersFilled || isDisabled}
          className={cx('finishQuizButton')}
          color='green'
          size='big'
        >
          {t('finish_quiz')}
        </Button>
        {showTimeOverModal && (
          <TestTimeOverModal
            active={showTimeOverModal}
            setActive={setShowTimeOverModal}
            attempts={
              currentData.settings.attempts_limit
                ? currentData.settings.attempts_limit - currentData.attempts.length
                : undefined
            }
            onSubmit={handleSubmit}
            onClose={handleCancel}
          />
        )}
      </div>
    )
  },
)

const QuizResult: FC<UserQuizProps.Props> = memo(
  ({ newAttempt, isLoading, refetch, currentData }) => {
    const analytics = useAnalytics()

    const attempt = useMemo(
      () => currentData?.attempts[currentData.attempts.length - 1],
      [currentData],
    )

    const [showAttemptsLimitModal, setShowAttemptsLimitModal] = useState<boolean>(false)

    const handleRepeat = () => {
      analytics.event(GOALS['course-repeat-quiz'].name)
      newAttempt().then(() => refetch())
    }

    const openModal = () => setShowAttemptsLimitModal(true)

    if (!attempt || !currentData) {
      return <Loader size='56' className={cx('loader')} />
    }

    return (
      <div className={cx('resultWrapper')}>
        <UserTestHeaderView
          attempt={currentData?.attempts.length}
          questions={attempt?.questions}
          maxPoints={attempt?.questions.length}
          started={attempt?.started_at}
          finished={attempt?.finished_at}
          passed={attempt?.passed}
          limit={currentData?.settings.attempts_limit || undefined}
          handleRepeat={handleRepeat}
          openModal={openModal}
          learningBlock={currentData.settings.learning_block}
          isLoading={isLoading}
        />
        {attempt?.questions.map((q: TQuestion, index: number) => (
          <TestCardView
            needExplain={currentData.settings.need_explain}
            key={q.id}
            question={q}
            order={index + 1}
          />
        ))}
        {showAttemptsLimitModal && (
          <AttemptsLimitModal
            active={showAttemptsLimitModal}
            setActive={setShowAttemptsLimitModal}
          />
        )}
      </div>
    )
  },
)

export const UserQuiz: React.FC<UserQuizProps.Own> = memo(({ stepId }) => {
  const sectionId = useAppSelector(selectSectionId)
  const theme = useAppSelector(selectTheme)
  const disableFetch = useAppSelector(selectDisableFetch)
  const { course_id = '' } = useParams()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const dispatch = useAppDispatch()

  const [forceShowResult, setForceShowResult] = useState(false)

  const [getMyScormCourseStatement] = themeStepsApi.useLazyGetStepProgressQuery()

  const {
    data: themeProgress,
    refetch: refetchThemeProgress,
    isLoading: isThemeProgressLoading,
  } = userStaticticsApi.useGetThemeProgressQuery(
    {
      course_id,
      theme_id: theme?.theme_id ?? '',
      section_id: sectionId ?? '',
    },
    { skip: !course_id || !theme || !sectionId || disableFetch },
  )

  useEffect(() => {
    dispatch(setDisableNext(isThemeProgressLoading))
  }, [isThemeProgressLoading, dispatch])

  const quizProgress = useMemo(() => {
    return themeProgress?.steps.find(s => s.step_id === stepId)
  }, [themeProgress, stepId])

  const [isStarted, setIsStarted] = useState<boolean>(quizProgress?.started ?? false)
  const [currentData, setCurrentData] = useState<QuizResponse>()

  const [createAttempt, { isLoading: isCreateAttemptLoading }] =
    userQuizApi.useCreateQuizAttemptMutation()

  const getState = useCallback(async () => {
    if (!quizProgress?.step_progress_id) return
    await setIsLoading(true)
    const state = (await getMyScormCourseStatement(quizProgress?.step_progress_id).unwrap()) || {}

    if (state) {
      //@ts-expect-error 'change types'
      setCurrentData(state)
    }
    await setIsLoading(false)
  }, [getMyScormCourseStatement, quizProgress?.step_progress_id, setCurrentData, setIsLoading])

  useEffect(() => {
    getState()
  }, [getState])

  const newAttempt = useCallback(async () => {
    if (!sectionId || !stepId || !theme?.theme_id) return
    await setIsStarted(false)
    setForceShowResult(false)
    await createAttempt({
      assigned_course_id: course_id,
      body: {
        section_id: sectionId,
        step_id: stepId,
        step_progress_id: quizProgress?.step_progress_id,
        theme_id: theme?.theme_id,
      },
    })
      .unwrap()
      .then(result => {
        setCurrentData(result)
        setIsStarted(true)
      })
  }, [course_id, createAttempt, quizProgress?.step_progress_id, sectionId, stepId, theme?.theme_id])

  const [progressId, setProgressId] = useState<string | undefined>()

  useEffect(() => {
    setProgressId(quizProgress?.step_progress_id)
  }, [quizProgress?.step_progress_id])

  useEffect(() => {
    if (quizProgress?.started !== undefined) {
      setIsStarted(quizProgress?.started)
    }
  }, [quizProgress?.started])

  // Логика определения какой компонент отображать
  const shouldShowQuiz = useMemo(() => {
    if (forceShowResult) return false
    if (!quizProgress?.started) return true

    const latestAttempt = currentData?.attempts[currentData.attempts.length - 1]
    return quizProgress.started && !quizProgress.finished_at && !latestAttempt?.finished_at
  }, [forceShowResult, quizProgress, currentData])

  if (!sectionId || !theme?.theme_id || !stepId) {
    return null
  }

  // Common props for both Quiz and QuizResult
  const commonProps = {
    stepId,
    courseId: course_id,
    sectionId,
    themeId: theme?.theme_id,
    currentData,
    isStarted,
    refetch: refetchThemeProgress,
    setProgressId,
    newAttempt,
    setCurrentData,
    setIsStarted,
    getState,
    isLoading: isThemeProgressLoading || isCreateAttemptLoading,
  }

  return (
    <div className={cx('wrapper')}>
      {shouldShowQuiz ? (
        <Quiz
          {...commonProps}
          progressId={progressId}
          isQuizStarted={quizProgress?.started}
          isLoading={isLoading}
          setForceShowResult={setForceShowResult}
        />
      ) : (
        <QuizResult {...commonProps} />
      )}
    </div>
  )
})
