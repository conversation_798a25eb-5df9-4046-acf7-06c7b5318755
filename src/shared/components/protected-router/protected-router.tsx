/* eslint-disable react-hooks/exhaustive-deps */
import { FC, Suspense, useEffect, useMemo } from 'react'
import styles from './protected-router.module.scss'
import classNamesBind from 'classnames/bind'
import { ProtectedRouterProps } from './protected-router.d'
import { Navigate, Route, Routes, useLocation } from 'react-router-dom'
import { Sidebar, LoadingPlug, RequireAuth } from '@/shared/components'
import { URLS } from '@/shared/configs/urls'
import React from 'react'
import { checkPermission, flattenObject } from './helper'
import { selectUserAuthorized, selectUserInited, setAuthSteps } from '@/store/slices/auth'
import { useAppDispatch, useAppSelector } from '@/store'
import { Loader } from 'shared/ui'
import { userAPI } from 'entities/employee'
import { useConfig, useUserPermissions } from '@/shared/hooks'
import { useDocumentTitle } from '@/shared/hooks/use-document-title'
import { useMediaQuery } from 'usehooks-ts'
import { TabBar } from '../tab-bar'
import { organizationAPI } from '@/entities/organization'

const cx = classNamesBind.bind(styles)

const { useGetOrganizationTitleQuery } = organizationAPI

export const ProtectedRouter: FC<ProtectedRouterProps.Props> = props => {
  const { routes } = props

  const isAuth = useAppSelector(selectUserAuthorized)
  const isInited = useAppSelector(selectUserInited)
  const dispatch = useAppDispatch()
  const { pathname } = useLocation()

  const {
    data: user,
    error,
    isError,
  } = useAppSelector(userAPI.endpoints.getUserInfo.select(undefined))

  const permissions = useUserPermissions(user?.role ?? null)

  const config = useConfig()

  const { data: myOrgTitle } = useGetOrganizationTitleQuery(undefined, { skip: !user })

  useDocumentTitle(myOrgTitle?.title ? myOrgTitle?.title : config?.title)

  useEffect(() => {
    if (!error) return

    if (!('status' in error)) return

    if (error?.status === 404) {
      dispatch(setAuthSteps({ twofa: true }))
    }
    if (error?.status === 400) {
      dispatch(setAuthSteps({ permissions: true }))
    }
  }, [error])

  const normalizedPermissions = useMemo(
    () => (permissions ? flattenObject(permissions) : {}),
    [permissions],
  ) as Record<string, true>

  const pages = useMemo(
    () =>
      routes.map(route => {
        const AuthWrapper = route.needAuth ? RequireAuth : React.Fragment

        const isAccessed = route?.permission
          ? checkPermission(normalizedPermissions, route.permission)
          : false

        if (!route.needAuth)
          return (
            <Route
              key={route.path}
              path={route.path}
              element={
                <Suspense fallback={<LoadingPlug />}>
                  <route.element />
                </Suspense>
              }
            />
          )

        if (!isAccessed)
          return (
            <Route
              key={route.path}
              path={route.path}
              element={<Navigate to={isAuth ? URLS.USER_MY_COURSES_PAGE : URLS.LOGIN_PAGE} />}
            />
          )

        const Content = () => (
          <Suspense fallback={<LoadingPlug />}>
            <div id='page-wrapper' className={cx('content')}>
              <div className={cx('page__wrapper')}>
                <route.element />
              </div>
            </div>
          </Suspense>
        )

        return (
          <Route
            path={route.path}
            key={route.path}
            element={
              <AuthWrapper>
                <Suspense fallback={<LoadingPlug />}>
                  <div className={cx('page', pathname.startsWith('/lk/user') && 'reverse')}>
                    <div
                      style={{
                        visibility: 'hidden',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        zIndex: -9999,
                        width: 0,
                        height: 0,
                        overflow: 'hidden',
                        fontWeight: 600,
                      }}
                    >
                      1
                    </div>
                    <div
                      style={{
                        visibility: 'hidden',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        zIndex: -9999,
                        width: 0,
                        height: 0,
                        overflow: 'hidden',
                        fontWeight: 500,
                      }}
                    >
                      2
                    </div>
                    <div
                      style={{
                        visibility: 'hidden',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        zIndex: -9999,
                        width: 0,
                        height: 0,
                        overflow: 'hidden',
                        fontFamily: 'GothamPro',
                      }}
                    >
                      3
                    </div>
                    <ContentNavigation />
                    <Content />
                  </div>
                </Suspense>
              </AuthWrapper>
            }
          />
        )
      }),
    [routes, normalizedPermissions, pathname],
  )
  const withPermissions = Boolean((permissions && normalizedPermissions) || isError)

  if (!isInited || !withPermissions)
    return (
      <div className={cx('centered')}>
        <Loader size='56' />
      </div>
    )

  return (
    <Routes>
      {pages}
      <Route
        path={'*'}
        key={'base-navigate'}
        element={<Navigate to={URLS.USER_MY_COURSES_PAGE} />}
      ></Route>
    </Routes>
  )
}

const ContentNavigation = () => {
  const isMobile = useMediaQuery('(max-width: 1024px)')
  const { pathname } = useLocation()

  return pathname.startsWith('/lk/user') && isMobile ? <TabBar /> : <Sidebar />
}
