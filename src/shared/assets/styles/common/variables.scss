:root {
  --color-gray-100: #0d1531;
  --color-gray-90: #343b54;
  --color-gray-80: #5c6585;
  --color-gray-70: #8e97af;
  --color-gray-60: #c9cedc;
  --color-gray-50: #e1e4eb;
  --color-gray-40: #f0f3f7;
  --color-gray-30: #ebeff2;
  --color-gray-20: #f9fafc;

  --stroke: #ebeff2;
  --stat-neutral-light: #e1e4eb;
  --stat-neutral: #8e97af;

  --color-primary: var(--injected-theme-color-primary);
  --color-primary-90: var(--injected-theme-color-primary-90);
  --color-primary-80: var(--injected-theme-color-primary-80);
  --color-primary-70: var(--injected-theme-color-primary-70);
  --color-primary-60: var(--injected-theme-color-primary-60);
  --color-primary-50: var(--injected-theme-color-primary-50);
  --color-primary-40: var(--injected-theme-color-primary-40);
  --color-primary-30: var(--injected-theme-color-primary-30);
  --color-primary-20: var(--injected-theme-color-primary-20);

  /*     Statistics     */
  --color-statistics-good: #3dbc87;
  --color-statistics-good-light: #d4f0e4;

  --color-statistics-warning: #ffc700;
  --color-statistics-warning-light: #fdf2d4;
  --color-statistics-warning-text: #ffc700;
  --color-statistics-warning-background: #fdf4dc;

  --color-statistics-bad: #ff8577;
  --color-statistics-bad-light: #ffdfe3;
  --color-statistics-bad-text: #ff8577;

  --color-statistics-neutral: var(--color-gray-80);
  --color-statistics-neutral-light: var(--color-gray-50);

  --color-statistics-complementary: #a259ff;
  /*     Statistics     */

  /*     Other     */
  --color-background: var(--color-gray-40);
  --color-surface: #fff;
  --color-border: var(--color-gray-30);
  --color-border-hover: var(--color-gray-60);

  --color-error: #ff586b;
  --color-warn: #ff586b;
  --color-code-editor: #1e1e1e;
  --text-good-text: #29a873;
  /*     Other     */

  /*     Gradients     */
  --gradient-primary: linear-gradient(
    58.48deg,
    var(--color-primary) 10.05%,
    var(--color-primary-70) 90.02%
  );
  /*     Gradients     */

  /*     Component     */

  --color-component-primary: var(--color-primary-90);
  --color-component-primary-hover: var(--color-primary);
  --color-component-primary-text: var(--color-surface);

  --color-component-warn: #ff586b;
  --color-component-warn-hover: #e84659;
  --color-component-warn-text: var(--color-surface);

  --color-component-secondary: var(--color-gray-60);
  --color-component-secondary-hover: var(--color-gray-70);
  --color-component-secondary-text: var(--color-surface);

  --color-component-disabled: var(--color-gray-50);
  --color-component-disabled-hover: var(--color-gray-50);
  --color-component-disabled-text: var(--color-gray-70);

  /*     Component     */

  --shadow-card: 0 0 16px rgba(0, 0, 0, 0.08);
  --shadow-border: inset 0 0 0 1px var(--color-border);
  --shadow-border-hover: inset 0 0 0 1px var(--color-border-hover);
  --tag-image-height: 160px;

  /*     Fonts     */
  --main-font-family: 'TT Norms Pro', 'Segoe UI', 'SF Pro Display', 'Roboto', sans-serif;

  --font-title-1-normal: 400 34px/44px var(--main-font-family);
  --font-title-1-medium: 500 34px/44px var(--main-font-family);
  --font-title-2-normal: 400 27px/36px var(--main-font-family);
  --font-title-2-medium: 500 27px/36px var(--main-font-family);
  --font-title-3-normal: 400 22px/28px var(--main-font-family);
  --font-title-3-medium: 500 22px/28px var(--main-font-family);
  --font-title-4-normal: 400 18px/24px var(--main-font-family);
  --font-title-4-medium: 500 18px/24px var(--main-font-family);
  --font-text-1-normal: 400 16px/20px var(--main-font-family);
  --font-text-1-medium: 500 16px/20px var(--main-font-family);
  --font-text-1-demibold: 600 16px/20px var(--main-font-family);
  --font-text-2-normal: 400 14px/20px var(--main-font-family);
  --font-text-2-medium: 500 14px/20px var(--main-font-family);
  --font-text-2-demibold: 600 14px/20px var(--main-font-family);
  --font-caption-1-normal: 400 13px/16px var(--main-font-family);
  --font-caption-1-medium: 500 13px/16px var(--main-font-family);
  --font-caption-1-demibold: 600 13px/16px var(--main-font-family);
  --font-caption-2-normal: 400 11px/16px var(--main-font-family);
  --font-caption-2-medium: 500 11px/16px var(--main-font-family);
  /*     Fonts     */

  --sidebar-width: 224px;
  --navbar-height: 54px;
  --page-horizontal-padding: 32px;
  --page-horizontal-padding-mobile: 16px;
  --page-container-small: 920px;
  --page-container: 1200px;
  --page-container-padding: 32px 0;

  --transition: ease 0.15s;

  --black: #000;
  --blue-grey: #8792ac;
  --brown-grey: #a0a0a0;
  --dark: #191b20;
  --dark-opacity: #a3a4a6;
  --gray: #eee;
  --gray-sub: #636b7e;
  --gark-gray: #3c3934;
  --white: #fff;
  --grey: #dcdcdc;
  --battleship-grey: #636b7e;
  --light-grey: #e5e5e5;
  --silver: #eee;
  --swan: #b5b5b5;

  --snackbars-success: #3dbc87;
  --snackbars-error: #ff708b;
  --snackbars-warning: #ffba69;
  --snackbars-neutral: #8e97af;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
div {
  font-family: var(--main-font-family);
}

h1 {
  font: var(--font-title-1-normal);
}

h2 {
  font: var(--font-title-2-normal);
}

h3 {
  font: var(--font-title-3-normal);
}

h4 {
  font: var(--font-title-4-normal);
}
