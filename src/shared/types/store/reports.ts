import { EStatus } from '@/shared/types/enums'

export interface IReportStatus {
  id: UUID
  type: string
  url: string | null
  status: EStatus
  withoutUrl?: boolean
}

export interface IReportsResponse {
  data: IReport[]
  total_count: number
  limit: number
  offset: number
}

export interface IReport {
  id: string
  status: EStatus
  created_at: string | null
  url?: string
  filename: string
}
