import { useEffect, useMemo, useState } from 'react'
import {
  DateCompanySummaryHashItem,
  calculateCompanyDateSummary,
} from '@/shared/helpers/statistics'
import { useTranslation } from 'react-i18next'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { phishingQueries } from '@/entities/phishing'
import { DataPoint, RangeItem } from '@/features/graphics'
import { DEFAULT_TIME_RANGE } from '@/features/graphics/model/constants'
import { calculateDays } from '@/shared/helpers/date'
import { organizationGatewayStatisticAPI, organizationStatisticAPI } from 'entities/statistic'
import { useUserOrganizationId } from '@/entities/employee'

const {
  useGetRiskStatisticsQuery,
  useGetProgressQuery,
  useGetProgressChangeQuery,
  useLazyGetProgressChangeQuery,
  useGetGroupQuery,
  useGetActiveCourseQuery,
  useGetPhishingStatisticsQuery,
  useGetRiskLevelChangeQuery,
  useGetRiskGroupsQuery,
} = organizationGatewayStatisticAPI

export const useStatistics = () => {
  const [activeProgressGroup, setActiveProgressGroup] = useState<string | null>(null)

  const [testingTimeRangeItem, setTestingTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )
  const [educationTimeRangeItem, setEducationTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )
  const userOrganizationId = useUserOrganizationId()
  const [riskLevelTimeRangeItem, setRiskLevelTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  const {
    data: progress,
    isLoading: isLoadingProgress,
    isFetching: isFetchingProgress,
  } = useGetProgressQuery(
    {
      organization_id: userOrganizationId ?? '',
    },
    {
      skip: !userOrganizationId,
    },
  )
  const {
    data: progressChange,
    isLoading: isLoadingProgressChange,
    isError: isErrorProgressStatistics,
  } = useGetProgressChangeQuery({
    organization_id: userOrganizationId,
    days_period: String(DEFAULT_TIME_RANGE[0]?.value),
  })

  const [triggerLazyProgress] = useLazyGetProgressChangeQuery()
  const {
    data: groups,
    isLoading: isLoadingProgressGroups,
    isFetching: isFetchingProgressGroups,
  } = useGetGroupQuery(userOrganizationId)
  const {
    data: activeCourses,
    isLoading: isLoadingActiveCourses,
    isFetching: isFetchingActiveCourses,
  } = useGetActiveCourseQuery(userOrganizationId ?? '', {
    skip: !userOrganizationId,
  })
  const { data: phishingStatistics, isLoading: isLoadingPhishingStatistics } =
    useGetPhishingStatisticsQuery(userOrganizationId)
  const {
    data: riskLevelChange,
    isLoading: isLoadingRiskLevelChange,
    isFetching: isFetchingRiskLevelChange,
  } = useGetRiskLevelChangeQuery({
    organization_id: userOrganizationId,
    days_period: String(
      calculateDays(riskLevelTimeRangeItem?.value ?? DEFAULT_TIME_RANGE[0]?.value),
    ),
  })

  const { data: riskStatistics, isLoading: isLoadingRiskStatistics } =
    useGetRiskStatisticsQuery(userOrganizationId)

  const { data: riskGroups, isLoading: isLoadingRiskGroups } =
    useGetRiskGroupsQuery(userOrganizationId)
  const { data: organization, isLoading: isLoadingOrganization } =
    organizationStatisticAPI.useGetOrganizationQuery(userOrganizationId ?? '', {
      skip: !userOrganizationId,
    })

  const { data: phishingCampaings, isLoading: isLoadingPhishingCampaings } =
    phishingQueries.useGetPhishingLastCampaignQuery(userOrganizationId)
  const [testingData, setTestingData] = useState<DataPoint[]>([])
  const [educationData, setEducationData] = useState<DataPoint[]>([])
  const [isLoadingTestingData, setIsLoadingTestingData] = useState(false)
  const [isFetchingTestingData, setIsFetchingTestingData] = useState(false)
  const [isLoadingEducationData, setIsLoadingEducationData] = useState(false)
  const [isFetchingEducationData, setIsFetchingEducationData] = useState(false)

  const isFetched = !(
    isLoadingProgress &&
    isLoadingProgressChange &&
    isLoadingPhishingStatistics &&
    isLoadingRiskStatistics &&
    isLoadingRiskLevelChange &&
    isLoadingProgressGroups &&
    isLoadingActiveCourses &&
    isLoadingOrganization
  )

  const { t } = useTranslation(['pages__statistics'])

  useEffect(() => {
    const getDataFn = async () => {
      setIsLoadingTestingData(true)
      setIsFetchingTestingData(true)
      try {
        const promises = [
          triggerLazyProgress({
            organization_id: userOrganizationId,
            days_period: testingTimeRangeItem?.value
              ? String(calculateDays(testingTimeRangeItem.value))
              : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
          }),
        ]
        const [{ data: progressData }] = await Promise.all(promises)

        if (!progressData) {
          setTestingData([])
          return
        }
        const { testingData } = calculateCompanyDateSummary(
          progressData.progress as DateCompanySummaryHashItem[],
        )
        setTestingData(testingData)
      } finally {
        setIsLoadingTestingData(false)
        setIsFetchingTestingData(false)
      }
    }
    getDataFn()
  }, [testingTimeRangeItem, triggerLazyProgress, userOrganizationId])

  useEffect(() => {
    setIsLoadingEducationData(true)
    setIsFetchingEducationData(true)

    const getDataFn = async () => {
      const promises = [
        triggerLazyProgress({
          organization_id: userOrganizationId ?? '',
          days_period: educationTimeRangeItem?.value
            ? String(calculateDays(educationTimeRangeItem?.value))
            : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
        }),
      ]
      const [{ data: progressData }] = await Promise.all(promises)
      if (!progressData) {
        setEducationData([])
        setIsLoadingEducationData(false)
        setIsFetchingEducationData(false)
        return
      }

      try {
        const promises = [
          triggerLazyProgress({
            organization_id: userOrganizationId,
            days_period: educationTimeRangeItem?.value
              ? String(calculateDays(educationTimeRangeItem.value))
              : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
          }),
        ]
        const [{ data: progressData }] = await Promise.all(promises)
        if (!progressData) {
          setEducationData([])
          return
        }

        const { learningData } = calculateCompanyDateSummary(
          progressData.progress as DateCompanySummaryHashItem[],
        )
        setEducationData(learningData)
      } finally {
        setIsLoadingEducationData(false)
        setIsFetchingEducationData(false)
      }
    }
    getDataFn()
  }, [educationTimeRangeItem, triggerLazyProgress, userOrganizationId])

  const progress_chart = useMemo(() => {
    const { learningData, testingData } = calculateCompanyDateSummary(progressChange?.progress)

    return [
      {
        title: t('commons:learning_progress'),
        color: 'blue',
        data: learningData,
      },
      {
        title: t('commons:test_progress'),
        color: 'green',
        data: testingData,
      },
    ]
  }, [progressChange, t])

  const risk_level_chart = useMemo(
    () => [
      {
        color: 'YELLOW',
        data: riskLevelChange?.risk_levels.map(i => ({ ...i })),
      },
    ],
    [riskLevelChange],
  )

  const phishing_chart = useMemo(
    () => ({
      sent: phishingStatistics?.sent || undefined,
      opened: phishingStatistics?.opened_letters || undefined,
      followed: phishingStatistics?.link_clicks || undefined,
      entered: phishingStatistics?.entered_data || undefined,
      attachments: phishingStatistics?.opened_attachments || undefined,
    }),
    [phishingStatistics],
  )

  const groups_chart_mini = useMemo(
    () => ({
      normal: groups?.normal || undefined,
      behind: groups?.behind || undefined,
    }),
    [groups],
  )

  const groups_chart = useMemo(
    () => [
      {
        title: t('commons:normal_plan'),
        color: 'green',
        value: groups?.normal || undefined,
        group: 'normal',
      },
      {
        title: t('commons:behind_plan'),
        color: 'yellow',
        value: groups?.behind || undefined,
        group: 'behind',
      },
    ],
    [groups, t],
  )

  const [openGenerateReport, setOpenGenerateReport] = useState(false)
  const analytics = useAnalytics()

  const onExcelClick = async () => {
    analytics.event('modules.statistics.report', {
      type: 'organization_progress',
      title: organization?.title,
    })
    setOpenGenerateReport(true)
  }

  const [openPhishingExcelReport, setOpenPhishingExcelReport] = useState(false)

  const onPhishingExcelClick = async () => {
    analytics.event('modules.statistics.report', {
      type: 'organization_phishing',
      title: organization?.title,
    })
    setOpenPhishingExcelReport(true)
  }

  const [openOverallReport, setOpenOverallReport] = useState(false)

  const onOverallClick = async () => {
    analytics.event('modules.statistics.report', {
      type: 'organization_overall',
      title: organization?.title,
    })
    setOpenOverallReport(true)
  }

  return {
    openOverallReport,
    setOpenOverallReport,
    openPhishingExcelReport,
    setOpenPhishingExcelReport,
    getOrgId: () => userOrganizationId ?? '',
    organization,
    onOverallClick,
    isLoadingOrganization,
    progress,
    isFetchingProgress,
    isLoadingProgress,
    t,
    activeCourses,
    isLoadingActiveCourses,
    isFetchingActiveCourses,
    phishingStatistics,
    isLoadingPhishingStatistics,
    riskStatistics,
    isLoadingRiskStatistics,
    setActiveProgressGroup,
    activeProgressGroup,
    groups_chart,
    openGenerateReport,
    setOpenGenerateReport,
    onExcelClick,
    risk_level_chart,
    isLoadingRiskLevelChange,
    isFetchingRiskLevelChange,
    isLoadingProgressChange,
    progress_chart,
    phishing_chart,
    isLoadingProgressGroups,
    isFetchingProgressGroups,
    onPhishingExcelClick,
    isFetched,
    isErrorProgressStatistics,
    riskGroups,
    isLoadingRiskGroups,
    phishingCampaings,
    isLoadingPhishingCampaings,
    groups_chart_mini,
    testingTimeRangeItem,
    setTestingTimeRangeItem,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    testingData,
    educationData,
    setRiskLevelTimeRangeItem,
    riskLevelTimeRangeItem,
    isLoadingTestingData,
    isFetchingTestingData,
    isLoadingEducationData,
    isFetchingEducationData,
  }
}
