import classNamesBind from 'classnames/bind'
import Skeleton from 'react-loading-skeleton'

import { EmployeesTable } from '@/shared/components/tables/employees-table'
import { DepartmentsTable } from '@/shared/components/tables/depatrments-table'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>iew } from '@/shared/ui'
import styles from './statistics.module.scss'
import { useStatistics } from './use-statistics'
import { EducationChartWidget, RiskLevelChartWidget, TestingChartWidget } from '@/features/graphics'
import { GeneralInformation } from '@/shared/components/general-information'
import { RiskDangerousWidget } from '@/shared/components/risk-dangerous-widget'
import { PhishingStatistics } from '@/shared/components/phishing-statistics'
import { LastNewsletter } from '@/shared/components/last-newsletter'
import { CoursesProgress, CoursesProgressTopline } from '@/shared/components/courses-progress'
import { getRiskLevelColor } from './helper'
import { HintReportModal } from '@/shared/modals/report-modal/hint-report-modal'
import { NofifyEmployyesByGroupModal } from '@/shared/modals/nofify-employyes-by-group-modal'

const cx = classNamesBind.bind(styles)

const Statistics = () => {
  const {
    activeCourses,
    activeProgressGroup,
    isLoadingActiveCourses,
    isLoadingOrganization,
    isLoadingPhishingStatistics,
    isLoadingRiskStatistics,
    onOverallClick,
    openOverallReport,
    openPhishingExcelReport,
    organization,
    phishingStatistics,
    progress,
    riskStatistics,
    setActiveProgressGroup,
    setOpenOverallReport,
    setOpenPhishingExcelReport,
    t,
    isLoadingProgress,
    isFetchingProgress,
    risk_level_chart,
    isLoadingRiskLevelChange,
    isFetchingRiskLevelChange,
    isLoadingProgressGroups,
    onPhishingExcelClick,
    phishing_chart,
    riskGroups,
    isLoadingRiskGroups,
    phishingCampaings,
    isLoadingPhishingCampaings,
    groups_chart,
    groups_chart_mini,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    testingTimeRangeItem,
    setTestingTimeRangeItem,
    testingData,
    educationData,
    setRiskLevelTimeRangeItem,
    riskLevelTimeRangeItem,
    isLoadingTestingData,
    isFetchingTestingData,
    isLoadingEducationData,
    isFetchingEducationData,
  } = useStatistics()

  const items = [
    {
      title: t('commons:learning_progress'),
      // TODO: Rewrite this piece of shit
      value: Number(progress?.overall?.toFixed(0)) || undefined,
      suffix: '%',
      color: undefined,
      loading: isLoadingProgress,
    },
    {
      title: t('commons:active_courses'),
      value: activeCourses?.active || 0,
      loading: isLoadingActiveCourses,
      hasActiveCourse: !!activeCourses?.active,
    },
    {
      title: t('commons:probability_of_incident'),
      value: phishingStatistics?.value?.toFixed(),
      color: phishingStatistics?.color,
      loading: isLoadingPhishingStatistics,
      suffix: '%',
    },
    {
      title: t('commons:risk_level'),
      value: riskStatistics?.risk_level?.toFixed(1),
      color: getRiskLevelColor(Number(riskStatistics?.risk_level?.toFixed(1)) ?? 0),
      loading: isLoadingRiskStatistics,
    },
  ]

  return (
    <section className={cx('wrapper')}>
      <div className={cx('header')}>
        <div className={cx('headerTitle')}>
          {organization?.title ? (
            `${t('title')} ${organization?.title}`
          ) : (
            <Skeleton width={200} height={40} />
          )}
        </div>
        <Button
          className={cx('generateOverallButton')}
          onClick={onOverallClick}
          disabled={isLoadingOrganization || !organization?.license?.users_count}
        >
          {t('commons:organization_report')}
        </Button>
        {openOverallReport && (
          <HintReportModal
            type='PDF'
            open={openOverallReport}
            setOpen={setOpenOverallReport}
            title={t('commons:organization_report')}
          />
        )}
      </div>
      <div className={cx('general')}>
        <h3 className={cx('general__title')}>{t('commons:total_information')}</h3>
        <GeneralInformation items={items} />
        <div className={cx('general__widgets')}>
          <div className={cx('general__widgets_sm')}>
            <RiskDangerousWidget {...riskGroups} isLoading={isLoadingRiskGroups} />
          </div>
          <div className={cx('general__widgets_xl')}>
            <RiskLevelChartWidget
              isLoading={isLoadingRiskLevelChange || isLoadingRiskStatistics}
              isFetching={isFetchingRiskLevelChange}
              chartProps={{
                data: risk_level_chart?.[0]?.data ?? [],
                type: 'redToGreen',
                customMax: 10,
                customMin: 0,
                dimension: '',
              }}
              onTimeRangeChange={v => setRiskLevelTimeRangeItem(v)}
              descriptionProps={{ risk: Number(riskStatistics?.risk_level?.toFixed(1)) ?? 0 }}
              defaultItem={riskLevelTimeRangeItem}
              className={cx('riskLevelChart')}
            />
          </div>
        </div>
      </div>
      <div className={cx('education')}>
        <h3 className={cx('education__title')}>{t('commons:testing_training_statistics')}</h3>
        <div className={cx('education__widgets')}>
          <CoursesProgress
            {...groups_chart_mini}
            active={activeCourses?.active || 0}
            finished={activeCourses?.completed || 0}
            isLoading={isLoadingActiveCourses || isLoadingProgressGroups}
            onClick={setActiveProgressGroup}
            topSlot={
              <CoursesProgressTopline
                isLoading={isLoadingActiveCourses || isLoadingProgressGroups}
                showArrow={false}
                active={activeCourses?.active || 0}
                finished={activeCourses?.completed || 0}
              />
            }
          />

          {activeProgressGroup && (
            <NofifyEmployyesByGroupModal
              activeProgressGroup={activeProgressGroup}
              setActiveProgressGroup={setActiveProgressGroup}
              groups_chart_mini={groups_chart_mini}
              groups_chart={groups_chart}
            />
          )}
          <TestingChartWidget
            className={cx('testingChart')}
            isLoading={(isLoadingProgress || isLoadingTestingData) && !testingData?.length}
            isFetching={isFetchingProgress || isFetchingTestingData}
            chartProps={{
              data: testingData,
              customMin: 0,
            }}
            descriptionProps={{ precent: progress?.quiz?.toFixed(1) ?? 0 }}
            onTimeRangeChange={v => setTestingTimeRangeItem(v)}
            defaultItem={testingTimeRangeItem}
          />
          <div className={cx('education__widgets_xl')}>
            <EducationChartWidget
              className={cx('educationChart')}
              isLoading={(isLoadingProgress || isLoadingEducationData) && !educationData?.length}
              isFetching={isFetchingProgress || isFetchingEducationData}
              chartProps={{
                data: educationData,
                customMin: 0,
              }}
              descriptionProps={{ percent: progress?.overall?.toFixed(1) ?? 0 }}
              onTimeRangeChange={v => setEducationTimeRangeItem(v)}
              defaultItem={educationTimeRangeItem}
            />
          </div>
        </div>
      </div>
      <div className={cx('phishing')}>
        <h3 className={cx('phishing__title')}>{t('commons:phishing_statistics')}</h3>
        <div></div>
        <div className={cx('phishing__widgets')}>
          <PhishingStatistics
            {...phishing_chart}
            onClick={onPhishingExcelClick}
            isLoading={isLoadingPhishingStatistics}
          />
          <LastNewsletter
            start_date={phishingCampaings?.start_date}
            id={phishingCampaings?.id}
            isLoading={isLoadingPhishingCampaings}
            {...phishingCampaings?.statistic}
            emptyNode={<p>{t('commons:no_phishing_data')}</p>}
          />
          {openPhishingExcelReport && (
            <HintReportModal
              type='phishing'
              open={openPhishingExcelReport}
              setOpen={setOpenPhishingExcelReport}
              title={t('commons:phishing_statistics')}
            />
          )}
        </div>
      </div>
      <div className={cx('row')}>
        <TabView
          className={cx('statisticsTable')}
          tabs={[
            <Tab id='departments' title={t('commons:departments')}>
              <DepartmentsTable />
            </Tab>,
            <Tab id='employees' title={t('commons:employees')}>
              <EmployeesTable />
            </Tab>,
          ]}
        ></TabView>
      </div>
    </section>
  )
}

export default Statistics
