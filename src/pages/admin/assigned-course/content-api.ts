import { globalBaseApi } from '@/store/services/endpoints/base'
import {
  ContentCourse,
  ContentCourseCurrentDetailChapters,
  ContentCourseCurrentStatistics,
  ContentCourseStatistics,
  NotifyEmployeesRequest,
  UpdateCoursePictureRequest,
} from './types'
import { formatDate } from './utils'
import { assignedCoursesApi, Course } from '../assigned-courses/api'
import { RootState } from '@/store'
import { UnknownAction } from '@reduxjs/toolkit'

export const contentApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getAssignedCourse: builder.query<ContentCourse, string>({
      query: id => ({
        url: '/learning/api/learning/assigned-courses/' + id,
      }),
      providesTags: (_1, _2, id) => ['assigned-course', { type: 'assigned-course', id }],
    }),
    getAssignedCourseStatistics: builder.query<ContentCourseStatistics, string>({
      query: id => ({
        url: `/learning/api/statistics/assigned-courses/${id}/groups`,
      }),
    }),
    getAssignedCourseProgress: builder.query<
      {
        data: ContentCourseCurrentStatistics[]
      },
      {
        id: string
        days_period?: number
      }
    >({
      query: ({ days_period, id }) => ({
        url: `/learning/api/statistics/assigned-courses/${id}/progress`,
        params: { days_period },
      }),
    }),
    getAssignedCourseCurrentProgress: builder.query<ContentCourseCurrentStatistics, string>({
      query: id => ({
        url: `/learning/api/statistics/assigned-courses/${id}/current-progress`,
      }),
    }),
    getAssignedCourseDetailStatisticsProgress: builder.query<
      ContentCourseCurrentDetailChapters,
      string
    >({
      query: id => ({
        url: `/learning/api/statistics/assigned-courses/${id}/detail-statistics`,
      }),
      transformResponse: (response: ContentCourseCurrentDetailChapters) => {
        const sections = response.data?.sort((a, b) => a?.order_id - b?.order_id)
        for (const section of sections) section?.themes?.sort((a, b) => a?.order_id - b?.order_id)

        return { data: sections }
      },
    }),
    notifyEmployyes: builder.mutation<unknown, NotifyEmployeesRequest>({
      query: ({ id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${id}/notify-message`,
        body,
        method: 'POST',
      }),
    }),
    completeAssignedCourseById: builder.mutation<unknown, string>({
      query: id => ({
        url: `/learning/api/learning/assigned-courses/${id}/complete`,
        method: 'POST',
      }),
      invalidatesTags: ['assigned-courses'],
    }),
    deleteAssignedCourseById: builder.mutation<unknown, string>({
      query: id => ({
        url: `/learning/api/learning/assigned-courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['assigned-courses'],
    }),
    updateAssignedCoursePicture: builder.mutation<unknown, UpdateCoursePictureRequest>({
      query: course => {
        const formData = new FormData()
        formData.append('title', course.image)

        return {
          url: `/learning/api/learning/assigned-courses/${course?.id}/picture`,
          method: 'POST',
          body: formData,
        }
      },
      invalidatesTags: ['assigned-courses'],
    }),
    updateAssignedCourse: builder.mutation<unknown, Partial<ContentCourse>>({
      query: course => {
        const formData = new FormData()
        if (course.title) {
          formData.append('title', course.title)
        }
        if (course.start_date) {
          formData.append('start_date', String(formatDate(course.start_date)))
        }
        if (course.end_date) {
          formData.append('end_date', String(formatDate(course.end_date)))
        }
        formData.append('description', course.description ?? '')
        if (typeof course.picture === 'object') {
          formData.append('image', course.picture)
        }
        formData.append('need_assigned_message', String(course.need_assigned_message ?? false))
        formData.append('need_notify_message', String(course.need_notify_message ?? false))

        return {
          url: `/learning/api/learning/assigned-courses/${course?.id}`,
          method: 'PATCH',
          body: formData,
        }
      },
      invalidatesTags: (_1, _2, { id }) => [{ type: 'assigned-course', id }],
      onQueryStarted: async ({ id }, { dispatch, getCacheEntry, getState, queryFulfilled }) => {
        if (!id) return

        queryFulfilled.then(() => {
          const cacheEntry = getCacheEntry()
          const allCacheEntries = assignedCoursesApi.util.selectCachedArgsForQuery(
            getState() as RootState,
            'getAssignedCoursesByOrganization',
          )
          allCacheEntries.forEach(param => {
            dispatch(
              assignedCoursesApi.util.updateQueryData(
                'getAssignedCoursesByOrganization',
                param,
                draft => {
                  return {
                    ...draft,
                    data: draft.data.map(item => {
                      console.log('item', item, item.id === id)
                      if (item.id === id) {
                        return {
                          ...item,
                          ...(cacheEntry.data as Course),
                        }
                      }
                      return item
                    }),
                  }
                },
              ) as unknown as UnknownAction,
            )
          })
        })
      },
    }),
  }),
  overrideExisting: true,
})
