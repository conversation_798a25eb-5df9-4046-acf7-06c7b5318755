import classNamesBind from 'classnames/bind'
import React, { FC, useMemo, useState } from 'react'
import { <PERSON><PERSON>c<PERSON><PERSON>, ButtonIcon, Tooltip } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import CalendarBoldIcon from '@/shared/ui/Icon/icons/components/CalendarBoldIcon'
import PeopleBoldIcon from '@/shared/ui/Icon/icons/components/PeopleBoldIcon'
import ChevroneBoldIcon from '@/shared/ui/Icon/icons/components/ChevroneBoldIcon'
import EmailPosted2Icon from '@/shared/ui/Icon/icons/components/EmailPosted2Icon'
import SuccessIcon from '@/shared/ui/Icon/icons/components/SuccessIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import styles from './assigned-course-new.module.scss'
import { useTranslation } from 'react-i18next'
import { IAssignedCourseProps } from '@/shared/types/store/course'
import { getAssignedCoursesDetailStatisticsEmployyesByIdUrl, URLS } from '@/shared/configs/urls'
import { Accordion, AccordionItem } from '@/shared/components/accordion'
import { CoursesProgress } from '@/shared/components/courses-progress'
import { formatNumber, isNumber } from '@/shared/helpers'
import { calculateDays, formatDateRange, getDateDifference } from '@/shared/helpers/date'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'
import { ChapterBodyElement, ChapterElement } from './chapter'
import { EducationChartWidget, RangeItem } from '@/features/graphics'
import { BarGraph } from '@/shared/components/graphics/education/bar-graph'
import { contentApi } from './content-api'
import { Link, useNavigate, useParams } from 'react-router-dom'
import Skeleton from 'react-loading-skeleton'
import { useAppDispatch, useAppSelector } from '@/store'
import { NofifyEmployyes } from '@/shared/modals/nofify-employyes'
import {
  selectEditCourseModalOpen,
  selectFilters,
  selectNofifyEmployeesModalOpen,
  selectReportModalOpen,
  setEditCourseModalOpen,
  setFilters,
  setNotifyEmployeesModalOpen,
  setReportModalOpen,
  setSelectedCountOfPeople,
} from './slice'
import { useForm } from 'react-hook-form'
import { Filters, ProgressGroup } from './types'
import { EditCourseModal } from './edit-course-modal'
import { ConfirmModal } from '@/shared/modals/confirm-modal'
import { HintReportModal } from '@/shared/modals/report-modal/hint-report-modal'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import z from 'zod'
import { setFilters as employeeSetFilters } from '../employee-course/slice'
import { isCourseByTag } from '@/entities/courses'
import { isThemeCompelted } from '@/entities/themeCourse'
import { DEFAULT_TIME_RANGE } from '../../../features/graphics/model/constants'
import { NotifyEmployeesForm } from '@/shared/components/notify-employees-form'

const cx = classNamesBind.bind(styles)

const getNotifyEmployyesSchema = ({ educationMsg }: { educationMsg: string }) =>
  z.object({
    education: z.string().array().min(1, { message: educationMsg }),
  })

type NofifyEmployyesModalProps = {
  initialValues?: Filters
}

const NofifyEmployyesModal = (props: NofifyEmployyesModalProps) => {
  const isOpen = useAppSelector(selectNofifyEmployeesModalOpen)
  const dispatch = useAppDispatch()
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const { course_id = '' } = useParams()
  const form = useForm<Filters>({ defaultValues: props.initialValues })
  const { data: courseStatistics } = contentApi.useGetAssignedCourseStatisticsQuery(course_id)

  const countOfPeople =
    form.watch('education')?.reduce((prev, curr) => {
      if (courseStatistics?.progress_groups[curr])
        return prev + courseStatistics?.progress_groups[curr]

      return prev
    }, 0) ?? 0

  const { add } = useNotification()

  const [notifyMutation, { isLoading }] = contentApi.useNotifyEmployyesMutation()

  const LEARNING_LIST = useMemo(
    () => [
      { id: 'normal', title: t('commons:normal_plan') },
      { id: 'behind', title: t('commons:behind_plan') },
    ],
    [t],
  )

  const handleSubmit = (data: { text: string }) => {
    const parse = getNotifyEmployyesSchema({
      educationMsg: t('notify.education_required'),
    }).safeParse(form.watch())

    if (parse.error) {
      form.setError('education', parse.error.issues[0])
      return
    }

    notifyMutation({
      id: course_id,
      body: {
        groups: form.watch('education'),
        need_not_started: false,
        message: data.text,
      },
    }).then(() => {
      form.reset()
      add({
        id: uuid(),
        status: 'success',
        message: t('notify.msg'),
      })
      dispatch(setNotifyEmployeesModalOpen(false))
    })
  }

  return (
    <NofifyEmployyes
      customDisabled={!form.watch('education')?.some(Boolean) || !countOfPeople || isLoading}
      topSlot={
        <NotifyEmployeesForm
          items={LEARNING_LIST}
          selectedValues={form.watch('education')?.map(String) || []}
          onChange={values => {
            const progressGroupValues = values.map(v => v as ProgressGroup)
            form.setValue('education', progressGroupValues)
          }}
          error={form.formState.errors.education?.message || ''}
        />
      }
      open={isOpen}
      setOpen={v => dispatch(setNotifyEmployeesModalOpen(!!v))}
      title={t('notify.title')}
      textPlaceholder={t('notify.textarea_placeholder')}
      count={countOfPeople}
      cancel={{
        text: t('notify.cancel'),
      }}
      submit={{
        text: t('notify.send'),
      }}
      onSubmit={handleSubmit}
    />
  )
}

const DeleteCourseById: FC<{ id: string }> = ({ id }) => {
  const [deleteMutation] = contentApi.useDeleteAssignedCourseByIdMutation()
  const [open, setOpen] = useState(false)
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const navigate = useNavigate()
  const { add } = useNotification()

  return (
    <>
      <ConfirmModal
        wrapperClassname={cx('actions__delete__wrapper')}
        footerClassname={cx('actions__delete__footer')}
        open={open}
        setOpen={setOpen}
        title={t('delete.title')}
        description={t('delete.description')}
        onClose={() => {
          setOpen(false)
        }}
        onConfirm={() => {
          deleteMutation(id).then(() => {
            navigate(URLS.ADMIN_LEARNING_PAGE)
            add({
              id: uuid(),
              status: 'success',
              message: t('delete.msg'),
            })
          })
        }}
        closeProps={{
          color: 'red',
          className: cx('actions__padding'),
        }}
        confirmProps={{
          color: 'gray',
          className: cx('actions__padding'),
        }}
      />
      <ButtonIcon
        onClick={() => setOpen(true)}
        iconSize='24'
        size='32'
        color='gray70'
        icon='trashBold'
      />
    </>
  )
}

const EditCourse: FC = () => {
  const { course_id = '' } = useParams()
  const { data: course } = contentApi.useGetAssignedCourseQuery(course_id)
  const [update] = contentApi.useUpdateAssignedCourseMutation()
  const editModalOpen = useAppSelector(selectEditCourseModalOpen)
  const dispatch = useAppDispatch()

  return (
    <>
      {editModalOpen && (
        <EditCourseModal
          initialValues={course}
          open={editModalOpen}
          onSubmit={data => update(data)}
          setOpen={v => dispatch(setEditCourseModalOpen(v))}
        />
      )}
      <ButtonIcon
        onClick={() => dispatch(setEditCourseModalOpen(true))}
        iconSize='24'
        size='32'
        color='gray70'
        icon='editBold'
      />
    </>
  )
}

const GenerateReport = () => {
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const { course_id = '' } = useParams()
  const dispatch = useAppDispatch()
  const reportModalOpen = useAppSelector(selectReportModalOpen)

  return (
    <>
      {reportModalOpen && (
        <HintReportModal
          type='courses'
          open={reportModalOpen}
          setOpen={v => dispatch(setReportModalOpen(!!v))}
          title={t('report.title')}
          objectID={course_id}
        />
      )}
      <button
        onClick={() => dispatch(setReportModalOpen(true))}
        className={cx('statistics__export', 'export')}
      >
        <span>{t('statistics.export')}</span>
        <IconWrapper size='20' color='gray80'>
          <DownloadIcon />
        </IconWrapper>
      </button>
    </>
  )
}

export const CompleteCourseById = ({ id }: { id: string }) => {
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const [complete] = contentApi.useCompleteAssignedCourseByIdMutation()
  const navigate = useNavigate()
  const { add } = useNotification()
  const [open, setOpen] = useState(false)

  return (
    <>
      <Tooltip content={<>{t('end_of_course')}</>}>
        <ButtonIcon
          icon='stopCircle'
          color='gray70'
          onClick={() => setOpen(true)}
          className={cx('info__section__actions__end')}
        />
      </Tooltip>
      <ConfirmModal
        wrapperClassname={cx('actions__delete__wrapper')}
        footerClassname={cx('actions__delete__footer')}
        open={open}
        setOpen={setOpen}
        title={t('complete.title')}
        description={t('complete.description')}
        onClose={() => {
          setOpen(false)
        }}
        onConfirm={() => {
          complete(id).then(() => {
            navigate(URLS.ADMIN_LEARNING_PAGE)
            add({
              id: uuid(),
              status: 'success',
              message: t('complete_msg'),
            })
          })
        }}
        closeProps={{
          color: 'red',
          className: cx('actions__padding'),
        }}
        closeText={t('complete.confirm')}
        confirmProps={{
          color: 'gray',
          className: cx('actions__padding'),
        }}
      />
    </>
  )
}

const AssignedCourse: React.FC<IAssignedCourseProps> = () => {
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const { course_id = '' } = useParams()
  const { data: course } = contentApi.useGetAssignedCourseQuery(course_id)
  const { data: courseStatistics, isLoading: isLoadingCourseStatistics } =
    contentApi.useGetAssignedCourseStatisticsQuery(course_id)
  const [educationTimeRangeItem, setEducationTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )
  const { data: courseProgressByPeriod } = contentApi.useGetAssignedCourseProgressQuery({
    id: course_id,
    days_period: educationTimeRangeItem?.value
      ? calculateDays(educationTimeRangeItem?.value)
      : calculateDays(DEFAULT_TIME_RANGE[0]?.value),
  })
  const filters = useAppSelector(selectFilters)
  const dispatch = useAppDispatch()

  const { data: courseCurrentProgress } =
    contentApi.useGetAssignedCourseCurrentProgressQuery(course_id)
  const { data: chapters } = contentApi.useGetAssignedCourseDetailStatisticsProgressQuery(course_id)
  const isOpen = useAppSelector(selectNofifyEmployeesModalOpen)
  const navigate = useNavigate()

  const title = course?.title ?? ''

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_LEARNING_PAGE,
        text: t('breadcrumbs.assigned_courses'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_CREATE_COURSE_PAGE,
        text: title,
        clickable: false,
        isLoading: !title,
      },
    ],
    [t, title],
  )

  const dateLocale = useLocaleForDates()
  const datesRange =
    course?.start_date && course?.end_date
      ? formatDateRange(new Date(course?.start_date), new Date(course?.end_date), dateLocale)
      : ''
  const remainingMonths = course?.end_date && getDateDifference(new Date(course?.end_date))

  const courseIsByTag = isCourseByTag(course?.end_date)

  return (
    <div className={cx('page')}>
      <div className={cx('info__section')}>
        <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumbs')} />
        <div className={cx('info__section__actions__wrapper')}>
          <h1 className={cx('title')}>{title ?? <Skeleton height={32} width={400} />}</h1>
          <div className={cx('info__section__actions')}>
            {course?.status !== 'completed' && <CompleteCourseById id={course_id} />}
            <EditCourse />
            <DeleteCourseById id={course_id} />
          </div>
        </div>
        <section className={cx('info__block')}>
          <div className={cx('info__block__element')}>
            <IconWrapper size='24' color='gray70'>
              <CalendarBoldIcon />
            </IconWrapper>
            <span>
              {datesRange && (
                <>
                  <span>{datesRange.startDate}</span>{' '}
                  {!courseIsByTag && <span> --- {datesRange.endDate}</span>}
                </>
              )}
              {!courseIsByTag && (
                <>
                  {remainingMonths &&
                    Number(remainingMonths?.months) > 0 &&
                    ` (${t('remaining.months', {
                      count: Number(remainingMonths?.months) ?? 0,
                    })})`}
                  {remainingMonths &&
                    !remainingMonths?.months &&
                    Number(remainingMonths?.days) > 0 &&
                    ` (${t('remaining.days', {
                      count: (Number(remainingMonths?.days) ?? 0) + 1,
                    })})`}
                </>
              )}
            </span>
          </div>
          {isNumber(course?.employees_count) && course?.employees_count > 0 && (
            <div className={cx('info__block__element')}>
              <IconWrapper size='24' color='gray70'>
                <PeopleBoldIcon />
              </IconWrapper>
              <span>{formatNumber(course?.employees_count)}</span>
            </div>
          )}
          <div className={cx('info__block__employees')}>
            <Link to={getAssignedCoursesDetailStatisticsEmployyesByIdUrl(course_id)}>
              <button className={cx('info__block__employees__button')}>
                <span>{t('employees')}</span>
                <IconWrapper color='primary' size='16'>
                  <ChevroneBoldIcon />
                </IconWrapper>
              </button>
            </Link>
          </div>
        </section>
      </div>
      <section className={cx('statistics__section')}>
        <div className={cx('statistics__title__wrapper')}>
          <h3 className={cx('title__small')}>{t('statistics.title')}</h3>
          <GenerateReport />
        </div>
        <section className={cx('statistics__graphics__wrapper')}>
          <div className={cx('statistics__graphics__first')}>
            {isOpen && <NofifyEmployyesModal initialValues={filters} />}
            {!courseIsByTag && (
              <CoursesProgress
                isLoading={isLoadingCourseStatistics}
                behind={courseStatistics?.progress_groups.behind}
                normal={courseStatistics?.progress_groups.normal}
                onClick={(type: ProgressGroup) => {
                  dispatch(setSelectedCountOfPeople(courseStatistics?.progress_groups?.[type] ?? 0))
                  dispatch(setFilters({ education: [type] }))
                  dispatch(setNotifyEmployeesModalOpen(true))
                }}
                bottomSlot={
                  courseStatistics?.progress_groups?.behind ? (
                    <div className={cx('send__wrapper')}>
                      <button
                        className={cx('send')}
                        onClick={() => {
                          dispatch(
                            setSelectedCountOfPeople(
                              courseStatistics?.progress_groups?.behind ?? 0,
                            ),
                          )
                          dispatch(
                            setFilters({
                              education: courseStatistics?.progress_groups?.behind
                                ? ['behind']
                                : [],
                            }),
                          )
                          dispatch(setNotifyEmployeesModalOpen(true))
                        }}
                      >
                        <IconWrapper color='primary'>
                          <EmailPosted2Icon />
                        </IconWrapper>
                        <span className={cx('send__text')}>
                          {t('commons:send_message_to_behind')}
                        </span>
                      </button>
                    </div>
                  ) : null
                }
              />
            )}
            <div className={cx('education__section')}>
              <h3 className={cx('title__small')}>{t('education_proccess')}</h3>
              <BarGraph
                bars={[
                  {
                    count: courseStatistics?.process_groups.completed ?? 0,
                    text: t('bars.end'),
                    className: cx('education__section__rows', 'education__section__rows_success'),
                    onClick: () => {
                      dispatch(employeeSetFilters({ status: ['completed'] }))
                      navigate(getAssignedCoursesDetailStatisticsEmployyesByIdUrl(course_id))
                    },
                  },
                  {
                    count: courseStatistics?.process_groups.in_process ?? 0,
                    text: t('bars.in-process'),
                    className: cx('education__section__rows', 'education__section__rows_progress'),
                    onClick: () => {
                      dispatch(employeeSetFilters({ status: ['in_process'] }))
                      navigate(getAssignedCoursesDetailStatisticsEmployyesByIdUrl(course_id))
                    },
                  },
                  {
                    count: courseStatistics?.process_groups.not_started ?? 0,
                    text: t('bars.not-started'),
                    className: cx(
                      'education__section__rows',
                      'education__section__rows_not-started',
                    ),
                    onClick: () => {
                      dispatch(employeeSetFilters({ status: ['not_started'] }))
                      navigate(getAssignedCoursesDetailStatisticsEmployyesByIdUrl(course_id))
                    },
                  },
                ]}
              />
            </div>
          </div>
          <EducationChartWidget
            isLoading={false}
            chartProps={{
              data:
                courseProgressByPeriod?.data?.map(v => ({
                  date: v.date,
                  value: v.statistics.overall,
                })) ?? [],
              customMin: 0,
            }}
            descriptionProps={{ percent: courseCurrentProgress?.statistics.overall ?? 0 }}
            graphicClassName={cx('chart')}
            onTimeRangeChange={v => setEducationTimeRangeItem(v)}
            defaultItem={educationTimeRangeItem}
          />
        </section>
      </section>
      {chapters && chapters.data?.length > 0 && (
        <section className={cx('chapters__section')}>
          <h3 className={cx('title__small')}>{t('chapters.title')}</h3>
          <div>
            <Accordion className={cx('accordion')} multiple>
              {[...chapters.data]
                .sort((a, b) => a.order_id - b.order_id)
                .map((chapter, i) => (
                  <AccordionItem
                    id={`${chapter.title}-${i}`}
                    key={`${chapter.title}-${i}`}
                    classNameHeader={cx('accordion__header')}
                    renderHeader={open => {
                      const isCompleted = chapter.quiz >= 100 && chapter.theory >= 100
                      const mustRenderTheory = chapter?.themes?.some(theme => theme.has_theory)
                      const mustRenderTesting = chapter?.themes?.some(theme => theme.has_quizes)

                      return (
                        <ChapterElement
                          renderTesting={() =>
                            !isCompleted ? (
                              mustRenderTesting ? (
                                <span>{t('testing', { count: chapter.quiz })}</span>
                              ) : null
                            ) : (
                              <span className={cx('completed__wrapper')}>
                                {t('all_passed')}{' '}
                                <IconWrapper color='primary'>
                                  <SuccessIcon />
                                </IconWrapper>
                              </span>
                            )
                          }
                          renderTheory={() =>
                            !isCompleted ? (
                              mustRenderTheory ? (
                                <span>{t('theory', { count: chapter.theory })}</span>
                              ) : null
                            ) : null
                          }
                          text={chapter.title || t('sections.empty_title')}
                          isOpen={open}
                        />
                      )
                    }}
                    classNameBody={cx('accordion__body')}
                  >
                    {chapter.themes.map(theme => {
                      const isCompleted = isThemeCompelted(theme)

                      return (
                        <ChapterBodyElement
                          key={theme.title}
                          className={cx('accordion__body__item')}
                          text={theme.title || t('sections.empty_title')}
                          renderTesting={() =>
                            !isCompleted ? (
                              theme?.has_quizes ? (
                                <span>{t('testing', { count: theme.quiz })}</span>
                              ) : null
                            ) : (
                              <span className={cx('completed__wrapper')}>
                                {t('all_passed')}
                                <IconWrapper color='primary'>
                                  <SuccessIcon />
                                </IconWrapper>
                              </span>
                            )
                          }
                          renderTheory={() =>
                            !isCompleted && theme.has_theory ? (
                              <span>{t('theory', { count: theme.theory })}</span>
                            ) : null
                          }
                        />
                      )
                    })}
                  </AccordionItem>
                ))}
            </Accordion>
          </div>
        </section>
      )}
    </div>
  )
}

export default AssignedCourse
