export type Filters = {
  education?: ProgressGroup[]
}

export type ProcessGroup = 'completed' | 'in_process' | 'not_started'
export type ProgressGroup = 'behind' | 'normal'
export type NotifyEmployeesRequest = {
  id: string
  body: {
    groups?: ProgressGroup[]
    need_not_started: boolean
    message: string
  }
}

export type ContentCourse = {
  id: string
  title: string
  description: string
  organization_id: string
  picture: string | File
  created_at: string
  start_date: string
  end_date: string
  status: string
  employees_count: number
  need_assigned_message: boolean
  need_notify_message: boolean
}

export type ContentCourseStatistics = {
  process_groups: {
    completed: number
    in_process: number
    not_started: number
  }
  progress_groups: {
    normal: number
    behind: number
  }
}

export type ContentCourseCurrentStatistics = {
  date: string
  statistics: {
    theory: number
    quiz: number
    overall: number
  }
}

export type ContentCourseCurrentDetailChapters = {
  data: {
    title: string
    theory: number
    quiz: number
    order_id: number
    themes: {
      title: string
      theory: number
      quiz: number
      has_quizes: boolean
      has_theory: boolean
      order_id: number
    }[]
  }[]
}

export interface UpdateCoursePictureRequest {
  id: UUID
  image: File
}
