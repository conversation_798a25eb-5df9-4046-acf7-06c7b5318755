/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useState } from 'react'
import styles from './tags-settings.module.scss'
import classNamesBind from 'classnames/bind'
import { v4 as uuid } from 'uuid'
import { Button, HelpIcon, Input, Switch, Tooltip } from '@/shared/ui'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { AssignCourseModal } from '@/shared/modals/assign-course-modal'
import { ETagType } from '@/shared/types/enums'
import { TagAction } from '@/shared/components'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  addAction,
  selectActiveTag,
  selectLoadedCourses,
  selectTagLifeDays,
  selectTagsActions,
  selectTagsModalValues,
  selectTagsSelectedCourses,
  selectTagsTurnOff,
  setActions,
  setInitialMessages,
  selectInitialMessages,
  setSelectedCourses,
  setTagsModalValue,
  setTurnOff,
  updateAction,
  updateInitialMessage,
  removeInitialMessage,
  setInitialMessage,
  selectInitialLifeDays,
  selectUsersTagTarget,
  setInitialUsersTagTarget,
  selectInitialUsersTagTarget,
  setUsersTagTarget,
  selectInitialDepartmentsTagTarget,
  selectDepartmentsTagTarget,
  selectExludeUsersIds,
  editTagById,
  selectAllTags,
  selectRiskLevelMax,
  selectRiskLevelMin,
} from '@/store/slices/tags'
import { useNotification } from '@/shared/contexts/notifications'
import { ITagAction } from '@/shared/types/store/tag'
import { SETTINGS_BY_ACTION } from './views'
import { areArraysEqual } from './helper'
import {
  tagsEmployeesApi,
  useAssignTagToEntitiesMutation,
  useCreateMessageMutation,
  useDeleteMessageMutation,
  useGetEmployeesByTagQuery,
  useRemoveTagFromEntitiesMutation,
  useToggleTagMutation,
  useUpdateLifeDaysMutation,
  useUpdateMessageMutation,
  useUpdateRiskLevelMutation,
} from '@/store/services/tags-employees-service'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
import { ColorPicker } from '@/shared/components/color-picker'
import { checkTagIsCustom } from '@/shared/helpers/organization'
import { ConfirmPhishingModal } from '@/shared/modals/confirm-phishing-modal'
import { useTranslation } from 'react-i18next'
import tagsSlice from '@/store/slices/tags/tags-slice'
import { coursesByTagApi } from '@/store/services/endpoints/new-tags-endpoint'
import { phishingMutations } from '@/entities/phishing'
import { hasArraysHaveSameElements } from '@/shared/utils'
import { organizationAPI } from 'entities/organization'
import { DATE_LOCALES } from '@/shared/helpers/date'
import { useLocale } from '@/shared/hooks/use-locale'

const cx = classNamesBind.bind(styles)

export interface ISave {
  life_days?: number
  min_risk_level?: number
  max_risk_level?: number
  turnOff?: boolean
  users_ids?: UUID[]
}

export const NewTagsSettings = () => {
  const { t } = useTranslation('pages__employees')
  const dispatch = useAppDispatch()
  const locale = useLocale()

  const organizationTags = useAppSelector(selectAllTags)
  const activeTag = useAppSelector(selectActiveTag)

  const { data: courses, isFetching: isCoursesFetching } = coursesByTagApi.useGetCoursesByTagQuery(
    {
      tag_id: activeTag?.id || '',
    },
    {
      skip: !activeTag?.id,
    },
  )

  // const { data: messages, isFetching: isMessagesFetching } = useGetMessagesByTagQuery(
  //   activeTag?.id || '',
  //   {
  //     skip: !activeTag?.id,
  //   },
  // )
  const { data: usersByTag, isFetching: isUsersByTagFetching } = useGetEmployeesByTagQuery(
    activeTag?.id || '',
    {
      skip: activeTag?.type !== ETagType?.arbitrary,
    },
  )

  const {
    data: tagActions,
    isError: isTagActionsError,
    isFetching: isTagActionsFetching,
  } = tagsEmployeesApi.useGetActionsByTagQuery(activeTag?.id || '', {
    skip: !activeTag?.id,
  })

  const [triggerActions] = tagsEmployeesApi.useLazyGetActionsByTagQuery()

  const actions = useAppSelector(selectTagsActions)
  const lifeDays = useAppSelector(selectTagLifeDays)
  const modals = useAppSelector(selectTagsModalValues)
  const entitiesInitialMessages = useAppSelector(selectInitialMessages.selectEntities)
  const initialLifeDays = useAppSelector(selectInitialLifeDays)
  const selectedCourses = useAppSelector(selectTagsSelectedCourses)

  const turnOff = useAppSelector(selectTagsTurnOff)
  const maxRiskLevel = useAppSelector(selectRiskLevelMax)
  const minRiskLevel = useAppSelector(selectRiskLevelMin)
  // будут считаться как "старые" курсы
  const loadedCourses = useAppSelector(state => selectLoadedCourses(state, activeTag?.id ?? ''))
  const usersTagTarget = useAppSelector(selectUsersTagTarget)
  const initialTagUsersIds = useAppSelector(selectInitialUsersTagTarget.selectIds)
  const departmentsTagTarget = useAppSelector(selectDepartmentsTagTarget)
  const initialTagDepartmentsIds = useAppSelector(selectInitialDepartmentsTagTarget.selectIds)
  const excludedUsersByTag = useAppSelector(selectExludeUsersIds)
  const [actionId, setActionId] = useState<UUID>()

  const handleOpen = useCallback(
    (id: UUID, courses: UUID[]) => {
      setActionId(id)
      const action = Object.values(actions).filter(action => action.id === id)[0]

      switch (action.type) {
        case 'course':
          dispatch(
            setTagsModalValue({
              value: true,
              name: 'courseSelector',
            }),
          )
          dispatch(setSelectedCourses(courses))
          break
        default:
          break
      }
    },
    [actions, dispatch],
  )

  const handleSelect = useCallback(
    (id: UUID, courses: UUID[]) => {
      dispatch(setSelectedCourses(courses))
      dispatch(updateAction({ id, body: { courses } }))
      dispatch(setTagsModalValue({ value: false, name: 'courseSelector' }))
    },
    [dispatch],
  )

  const [updateMessage] = useUpdateMessageMutation()
  const [createMessage] = useCreateMessageMutation()
  const [deleteMessage] = useDeleteMessageMutation()
  const [updateLifeDays] = useUpdateLifeDaysMutation()
  const [updateRiskLevel] = useUpdateRiskLevelMutation()
  const [createCourses] = coursesByTagApi.useAssignCoursesByTagMutation()
  const [updateCourses] = coursesByTagApi.useUpdateCoursesByTagMutation()
  const [deleteCourses] = coursesByTagApi.useDeleteCoursesByTagMutation()
  const [toggleTag] = useToggleTagMutation()
  const [assignTagToEntities] = useAssignTagToEntitiesMutation()
  const [removeTagFromEntities] = useRemoveTagFromEntitiesMutation()
  const { add } = useNotification()

  const [createAutophishingTemplatesByTag] =
    phishingMutations.useCreateAuthophishingTemplatesByTagIdMutation()

  const [updateAutophishingTemplatesByTag] =
    phishingMutations.useUpdateAuthophishingTemplatesByTagIdMutation()

  const [updateAutophishingDeferByTag] =
    phishingMutations.useUpdateAuthophishingDeferByTagIdMutation()

  const [deleteAutophishingTemplatesByTag] =
    phishingMutations.useDeleteAuthophishingTemplatesByTagIdMutation()

  const [inviteReminderConfig] = organizationAPI.useInviteReminderConfigMutation()

  const [isFetching, setIsFetching] = useState(false)
  const [tagTitle, setTagTitle] = useState(activeTag?.title)
  const [tagColor, setTagColor] = useState(activeTag?.color)

  useEffect(() => {
    if (!activeTag) return

    setTagTitle(activeTag.title)
    setTagColor(activeTag.color)
    dispatch(tagsSlice.actions.setExcludeUsersIds([]))
  }, [activeTag, dispatch])

  const handleTitleChange = useCallback((value: string) => {
    setTagTitle(value)
  }, [])

  const handleColorChange = useCallback((color: string) => {
    setTagColor(color)
  }, [])

  const isCustomTag = useMemo(() => {
    if (!activeTag) return false

    return checkTagIsCustom(activeTag)
  }, [activeTag])

  const isEntitiesFetching = isCoursesFetching || isTagActionsFetching || isUsersByTagFetching

  useEffect(() => {
    const newActions: ITagAction[] = []

    if (courses?.length) {
      newActions.push({
        type: 'course',
        courses: courses.map(c => c.id),
        oldCourses: courses.map(c => c.id),
        id: uuid(),
        isNew: false,
      })
    }

    if (tagActions?.messages?.length) {
      tagActions?.messages.forEach(m => {
        newActions.push({
          type: 'letter',
          theme: m.theme,
          text: m.text,
          id: m.id,
          isNew: false,
        })
      })

      dispatch(setInitialMessages(tagActions?.messages))
    }

    const isAvailableTag =
      activeTag?.type === ETagType?.newbie ||
      activeTag?.type === ETagType.not_registered ||
      isCustomTag

    if (!isTagActionsError && isAvailableTag && tagActions && tagActions.phishing) {
      const hasPhishingData =
        (tagActions.phishing.email_templates && tagActions.phishing.email_templates.length > 0) ||
        tagActions.phishing.campaign_id ||
        (tagActions.phishing.defer_by !== undefined && tagActions.phishing.defer_by > 0)

      if (hasPhishingData) {
        newActions.push({
          type: 'phishing',
          id: uuid(),
          phishing_templates: tagActions.phishing.email_templates || [],
          phishing_templates_campaign_id: tagActions.phishing.campaign_id,
          defer_by: tagActions.phishing.defer_by || 0,
          isNew: false,
        })
      }
    }

    const notRegisteredTag = organizationTags?.data?.find(t => t.type === ETagType.not_registered)

    if (
      activeTag?.type === ETagType.not_registered &&
      notRegisteredTag?.settings?.mail_invite_interval &&
      notRegisteredTag?.settings?.mail_invite_time
    ) {
      const registration_link_time = new Date(
        `1970-01-01T${notRegisteredTag?.settings?.mail_invite_time}.000Z`,
      ).toLocaleTimeString(DATE_LOCALES[locale as keyof typeof DATE_LOCALES], {
        hour: '2-digit',
        minute: '2-digit',
      })

      newActions.push({
        type: 'registration_link',
        id: uuid(),
        isNew: false,
        registration_link_days: notRegisteredTag?.settings?.mail_invite_interval / 60 / 60 / 24,
        registration_link_time,
      })
    }

    dispatch(setActions(newActions))

    if (courses) dispatch(setSelectedCourses(courses.map(c => c.id)))
  }, [dispatch, tagActions, courses, activeTag?.type, isTagActionsError, isCustomTag, locale])

  useEffect(() => {
    if (isUsersByTagFetching || !usersByTag) return

    dispatch(setInitialUsersTagTarget(usersByTag.map(user => user.id)))
    dispatch(
      setUsersTagTarget(
        usersByTag.map(tagTarget => ({
          id: tagTarget.id,
          isNew: false,
        })),
      ),
    )
  }, [dispatch, isUsersByTagFetching, usersByTag])

  const save = useCallback(
    async ({ life_days, max_risk_level, min_risk_level, turnOff }: ISave) => {
      setIsFetching(true)
      let wasNotification = false
      try {
        if (!activeTag) return
        if (turnOff !== undefined && activeTag?.is_active !== turnOff) {
          await toggleTag({
            tagID: activeTag?.id,
            toggle: turnOff,
          })

          setTimeout(() => {
            tagsEmployeesApi.util.invalidateTags(['employeesTags'])
          }, 100)

          if (!turnOff) {
            dispatch(setInitialUsersTagTarget([]))
            dispatch(setUsersTagTarget([]))
          }
        }

        if (
          isCustomTag &&
          ((tagTitle && tagTitle !== activeTag.title) || (tagColor && tagColor !== activeTag.color))
        ) {
          await dispatch(
            editTagById({
              tagId: activeTag?.id,
              body: { title: tagTitle || '', color: tagColor || '' },
            }),
          ).unwrap()
        }

        const deletedUsersTagTargetIds = initialTagUsersIds.filter(initialUserId =>
          usersTagTarget?.every(userTagTarget => userTagTarget.id !== initialUserId),
        )

        const deletedDepartmentsTagTargetIds = initialTagDepartmentsIds.filter(
          initialDepartmentId =>
            departmentsTagTarget?.every(
              departmentTagTarget => departmentTagTarget.id !== initialDepartmentId,
            ),
        )

        const isHaveDeletedUsers =
          deletedUsersTagTargetIds.length > 0 || deletedDepartmentsTagTargetIds.length > 0

        let deletePromises: Promise<void> | undefined

        if (isHaveDeletedUsers) {
          deletePromises = removeTagFromEntities({
            tagID: activeTag?.id,
            usersIds: deletedUsersTagTargetIds,
            departmentsIds: deletedDepartmentsTagTargetIds,
          }).unwrap()
        }

        let assignTagPromise: Promise<void> | undefined

        const newUsersTagTargetIds = usersTagTarget
          ? usersTagTarget?.filter(user => user.isNew).map(user => user.id)
          : []

        const newDepartmentsTagTargetIds = departmentsTagTarget
          ? departmentsTagTarget
              ?.filter(department => department.isNew)
              .map(department => department.id)
          : []

        const isHaveNewUsers =
          newUsersTagTargetIds.length > 0 || newDepartmentsTagTargetIds.length > 0

        if (turnOff && isHaveNewUsers) {
          assignTagPromise = assignTagToEntities({
            tagID: activeTag?.id,
            usersIds: newUsersTagTargetIds,
            departmentsIds: newDepartmentsTagTargetIds,
            needOnBoarding: true,
            exclude_users_ids: excludedUsersByTag,
          }).unwrap()
        }

        if (turnOff) {
          await Promise.all([deletePromises, assignTagPromise]).catch(error => {
            add({
              message: t('commons:error_occurred') + error,
              status: 'error',
              id: uuid(),
            })
          })
        }

        let coursePromise: Promise<void> | undefined
        const courseAction = actions.filter(a => a.type === 'course')[0]

        const tag_id = activeTag?.id

        if (!courseAction && !!loadedCourses?.length) {
          coursePromise = deleteCourses({ tag_id: tag_id })
            .unwrap()
            .then(() => {
              dispatch(
                coursesByTagApi.util.updateQueryData(
                  'getCoursesByTag',
                  { tag_id: tag_id },
                  () => [],
                ),
              )
            })
        }

        const isCoursesUpdated =
          courseAction &&
          courseAction?.courses &&
          courseAction?.oldCourses &&
          !hasArraysHaveSameElements(courseAction.courses, courseAction.oldCourses)

        if (!loadedCourses?.length && !!courseAction && !!courseAction.courses && !coursePromise) {
          coursePromise = createCourses({
            tag_id: activeTag?.id,
            courses_ids: courseAction.courses,
          })
            .unwrap()
            .then(() => {
              dispatch(
                coursesByTagApi.util.updateQueryData(
                  'getCoursesByTag',
                  { tag_id: activeTag?.id },
                  () => courseAction?.courses?.map(id => ({ id })) as unknown as any,
                ),
              )
            })
        }

        if (!!loadedCourses?.length && isCoursesUpdated) {
          coursePromise = updateCourses({
            tag_id: activeTag?.id,
            courses_ids: courseAction.courses as string[],
          }).unwrap()
        }

        if (coursePromise) {
          await Promise.all([coursePromise]).catch(error => {
            add({
              message: t('commons:error_occurred') + error,
              status: 'error',
              id: uuid(),
            })
          })
        }

        if (typeof life_days === 'number' && life_days !== initialLifeDays) {
          await updateLifeDays({ tagID: activeTag?.id, life_days })
        }
        if (typeof min_risk_level === 'number' && typeof max_risk_level === 'number') {
          await updateRiskLevel({
            tagID: activeTag?.id,
            min_risk_level,
            max_risk_level,
          })
        }

        const oldLettersID = actions.filter(a => a.type === 'letter' && !a.isNew).map(a => a.id)

        const deletedMessage = tagActions?.messages?.filter(m => {
          if (oldLettersID.indexOf(m.id) === -1) {
            return true
          }

          return false
        })

        if (deletedMessage) {
          for (let i = 0; i < deletedMessage?.length; i++) {
            const action = deletedMessage[i]

            await deleteMessage({
              tagID: activeTag?.id,
              messageID: action.id,
            })

            dispatch(removeInitialMessage(action.id))
          }
        }

        let isPhishingTemplateExists = false

        for (let i = 0; i < actions?.length; i++) {
          const action = actions[i]

          if (action.type === 'letter' && !action.isNew) {
            if (!action.theme || !action.text) {
              add({
                message: t('letter_action_required_fields'),
                status: 'error',
                id: uuid(),
              })
              wasNotification = true

              return
            }
            const initialMessage = entitiesInitialMessages[action.id]
            const isMessageUpdated =
              initialMessage?.text !== action.text || initialMessage?.theme !== action.theme

            if (isMessageUpdated) {
              await updateMessage({
                tagID: activeTag?.id,
                messageID: action.id,
                body: {
                  theme: action.theme || '',
                  text: action.text || '',
                },
              })

              dispatch(
                updateInitialMessage({
                  id: action.id,
                  text: action.text || '',
                  theme: action.theme || '',
                }),
              )
            }
          }
          if (action.type === 'letter' && !!action.isNew) {
            if (!action.theme || !action.text) {
              add({
                message: t('letter_action_required_fields'),
                status: 'error',
                id: uuid(),
              })
              wasNotification = true

              return
            }

            await createMessage({
              tagID: activeTag?.id,
              body: {
                theme: action.theme || '',
                text: action.text || '',
              },
            })

            dispatch(
              setInitialMessage({
                id: activeTag?.id,
                theme: action.theme || '',
                text: action.text || '',
              }),
            )
          }

          if (action.type === 'registration_link') {
            const day_interval = action?.registration_link_days || 1
            // 08:56:09.623Z
            let time = '00:00:00.000Z'

            if (action?.registration_link_time) {
              const [hours, minutes] = action.registration_link_time.split(':')
              const now = new Date()
              now.setHours(parseInt(hours), parseInt(minutes), 0, 0)

              const utcHours = String(now.getUTCHours()).padStart(2, '0')
              const utcMinutes = String(now.getUTCMinutes()).padStart(2, '0')
              time = `${utcHours}:${utcMinutes}:00.000Z`
            }

            await inviteReminderConfig({
              enable: true,
              day_interval,
              time,
            }).unwrap()
          }
          /*
          cases:
            1) загружаем в первый раз (CREATE)
            2) удалили, нажали сохранить, создали, нажали сохранить (DELETE,CREATE)
            3) в создавшейся просто меняем кол-во шаблонов (PATCH)
            4) просто удалили (DELETE)

          Также сервер не успевает обновить шаблоны, если их перезапрашивать сразу по
            PATCH,CREATE...
          */

          if (action.phishing_templates && action?.phishing_templates?.length >= 0) {
            isPhishingTemplateExists = true

            const tagId = activeTag?.id
            if (!tagId) continue

            const isEquals =
              areArraysEqual(
                action.phishing_templates || [],
                tagActions?.phishing?.email_templates || [],
              ) && tagActions?.phishing?.defer_by == action.defer_by

            if (isEquals) continue

            const ids = action?.phishing_templates || []

            if (ids?.length === 0 && !action.isNew) {
              await deleteAutophishingTemplatesByTag(activeTag?.id).unwrap()
              continue
            }

            if (action.defer_by !== undefined && !action.isNew) {
              await updateAutophishingDeferByTag({
                id: tagId,
                defer_by: action.defer_by,
              }).unwrap()
            }

            if (ids?.length > 0 && !action.isNew) {
              await updateAutophishingTemplatesByTag({
                id: tagId,
                ids,
              }).unwrap()
            } else if (ids?.length > 0 && action.isNew) {
              const tagId = activeTag?.id
              await createAutophishingTemplatesByTag({
                id: tagId,
                ids,
                defer_by: action.defer_by || undefined,
              }).unwrap()

              setTimeout(() => {
                triggerActions(tagId)
              }, 7000)

              wasNotification = true
              // add({
              //   message: 'Действие сохранено, рассылка будет создана в течение минуты',
              //   status: 'success',

              //   id: uuid(),
              // })
              dispatch(updateAction({ id: action.id, body: { isNew: false } }))
            }
          }
        }

        if (
          activeTag?.type === ETagType.not_registered &&
          !actions.find(a => a.type === 'registration_link')
        ) {
          await inviteReminderConfig({
            enable: false,
            day_interval: 0,
            time: '00:00:00.000Z',
          }).unwrap()
        }

        if (
          tagActions &&
          tagActions.phishing?.email_templates &&
          !isPhishingTemplateExists &&
          !isTagActionsError
        ) {
          await deleteAutophishingTemplatesByTag(activeTag?.id)
        }
      } catch (error) {
        add({
          message: t('commons:error_occurred') + error,
          status: 'error',
          id: uuid(),
        })
      } finally {
        if (!wasNotification)
          add({
            message: t('changes_saved_successfully'),
            status: 'success',
            id: uuid(),
          })
        setIsFetching(false)
      }
    },
    [
      actions,
      activeTag,
      add,
      assignTagToEntities,
      createAutophishingTemplatesByTag,
      createCourses,
      createMessage,
      deleteCourses,
      deleteMessage,
      entitiesInitialMessages,
      initialLifeDays,
      initialTagUsersIds,
      loadedCourses?.length,
      toggleTag,
      updateAutophishingTemplatesByTag,
      updateCourses,
      updateLifeDays,
      updateMessage,
      updateRiskLevel,
      usersTagTarget,
      tagTitle,
      tagColor,
      tagActions,
      isTagActionsError,
    ],
  )

  const checkSaving = useCallback((): boolean => {
    const courseAction = actions.filter(a => a.type === 'course')[0]

    if (!courseAction && !!courses?.length) {
      return true
    }

    if (!courseAction && !courses?.length) {
      return false
    }

    if ((!courseAction.courses?.length || !!courseAction.courses.length) && !courses) {
      return false
    }

    let hasDeleted = false

    courses?.forEach(c => {
      if (courseAction.courses?.indexOf(c.id) === -1) {
        hasDeleted = true
      }
    })

    return hasDeleted
  }, [actions, courses])

  const handleSave = useCallback(() => {
    const body: ISave = {
      turnOff,
    }

    if (activeTag?.type === 'newbie') body.life_days = +lifeDays
    if (activeTag?.type === 'risk_group') {
      body.min_risk_level = minRiskLevel
      body.max_risk_level = maxRiskLevel
    }

    save(body)
  }, [turnOff, activeTag?.type, lifeDays, save, maxRiskLevel, minRiskLevel])

  const handleCheckConfirm = () => {
    if (activeTag?.is_active && !turnOff) {
      dispatch(setTagsModalValue({ value: true, name: 'turnOff' }))
      // || checkScormSaving()
    } else if (checkSaving()) {
      dispatch(setTagsModalValue({ value: true, name: 'reassignCourses' }))
    } else if (actions.find(i => i.type === 'phishing')) {
      dispatch(setTagsModalValue({ value: true, name: 'phishingWarning' }))
    } else {
      handleSave()
    }
  }

  const handleConfirm = useCallback(
    async (isClose: boolean) => {
      if (!isClose) handleSave()
    },
    [handleSave],
  )

  const handleConfirmReassign = useCallback(
    async (isClose: boolean) => {
      if (isClose) return

      if (actions.find(i => i.type === 'phishing')) {
        dispatch(setTagsModalValue({ value: true, name: 'phishingWarning' }))
      } else {
        handleSave()
      }
    },
    [handleSave, actions, dispatch],
  )

  const handleConfirmTurnOff = async (isClose: boolean) => {
    if (isClose) return
    // || checkScormSaving()
    if (checkSaving()) {
      dispatch(setTagsModalValue({ value: true, name: 'reassignCourses' }))
    } else if (actions.find(i => i.type === 'phishing')) {
      dispatch(setTagsModalValue({ value: true, name: 'phishingWarning' }))
    } else {
      handleSave()
    }
  }

  return (
    <div className={cx('wrapper')}>
      <>
        <>
          <div className={cx('title')}>{t('tag_name')}</div>
          <div className={cx('inputTitleWrapper')}>
            <Input
              placeholder={t('tag_name')}
              className={cx('inputTitle')}
              value={tagTitle || ''}
              onChange={handleTitleChange}
              disabled={!isCustomTag || isFetching || isEntitiesFetching}
              fullWidth
            />
            {isCustomTag ? (
              <Tooltip
                initOpen={true}
                content={t('set_color')}
                className={cx('color')}
                tooltipClassname={cx('colorTooltip')}
              >
                <ColorPicker initColor={tagColor || ''} onChange={handleColorChange} />
              </Tooltip>
            ) : (
              <ColorPicker initColor={tagColor || ''} className={cx('color')} disabled />
            )}
          </div>
        </>
        {(isFetching || isEntitiesFetching) && (
          <div className={cx('loader__wrapper')}>
            <Skeleton width={'70%'} height={20} enableAnimation />
            <Skeleton width={'40%'} height={20} />
            <Skeleton width={'50%'} height={24} />
            <Skeleton width={'100%'} height={28} />
            <Skeleton width={'80%'} height={28} enableAnimation duration={300} />
            <Skeleton
              width={'100%'}
              borderRadius={12}
              height={140}
              enableAnimation
              duration={300}
            />
            <Skeleton
              width={'100%'}
              borderRadius={12}
              height={140}
              enableAnimation
              duration={300}
            />
            <Skeleton width={'40%'} height={28} />
            <div className={cx('button')}>
              <Skeleton width={'100%'} height={40} />
            </div>
          </div>
        )}
        {!isFetching && !isEntitiesFetching && (
          <>
            <div className={cx('title', 'titleWithHelp')}>
              {t('enable_disable_tag')}
              <HelpIcon text={t('off_description')} />
            </div>
            <div className={cx('turnOffWrapper')}>
              <Switch
                text={t('on_tag')}
                customValue={turnOff}
                onChange={() => {
                  dispatch(setTurnOff(!turnOff))
                }}
              />
            </div>
            {activeTag && <div key={activeTag?.id}>{SETTINGS_BY_ACTION[activeTag.type]}</div>}
            <div className={cx('title')}>{t('action_for_tag')}</div>
            <div className={cx('actionsWrapper')}>
              {actions &&
                actions.map((a, i) => (
                  <TagAction
                    key={a.id}
                    index={i}
                    info={a}
                    openModal={handleOpen}
                    activeTag={activeTag !== null ? activeTag : undefined}
                  />
                ))}
            </div>
            <Button
              leftIcon='plus'
              size='small'
              color='darkGray'
              // eslint-disable-next-line i18next/no-literal-string
              leftIconColor={!turnOff ? 'gray60' : undefined}
              disabled={!turnOff}
              title={!turnOff ? t('need_turn_on') : ''}
              onClick={() => {
                dispatch(
                  addAction({
                    type: 'none',
                    id: uuid(),
                    isNew: true,
                  }),
                )
              }}
            >
              {t('add_action')}
            </Button>
            <Button className={cx('saveButton')} onClick={handleCheckConfirm}>
              {t('commons:save')}
            </Button>
            {activeTag && activeTag.type === ETagType.risk_group && (
              <div className={cx('help')}>{t('update_description')}</div>
            )}
          </>
        )}
      </>

      {modals.reassignCourses && (
        <DeleteModal
          onCloseWithoutData={handleConfirmReassign}
          title={t('reassign_courses')}
          text={t('reassign_description')}
          active={modals.reassignCourses}
          setActive={v => {
            const value = Boolean(v)

            dispatch(setTagsModalValue({ value, name: 'reassignCourses' }))
          }}
        />
      )}
      {modals.phishingWarning && (
        <ConfirmPhishingModal
          onCloseWithoutData={handleConfirm}
          title={t('phishing_description')}
          text=''
          active={modals.phishingWarning}
          setActive={v => {
            const value = Boolean(v)

            dispatch(setTagsModalValue({ value, name: 'phishingWarning' }))
          }}
        />
      )}
      {modals.turnOff && (
        <DeleteModal
          onCloseWithoutData={handleConfirmTurnOff}
          title={t('confirm_tag_off')}
          text={t('confirm_tag_off_description')}
          active={modals.turnOff}
          setActive={v => {
            const value = Boolean(v)

            dispatch(setTagsModalValue({ value, name: 'turnOff' }))
          }}
        />
      )}

      {modals.courseSelector && !!actionId && (
        <AssignCourseModal
          actionID={actionId}
          active={modals.courseSelector}
          tagId={activeTag?.id}
          setActive={() => {
            dispatch(setTagsModalValue({ value: false, name: 'courseSelector' }))
          }}
          selected={selectedCourses}
          handleSelect={handleSelect}
        />
      )}
    </div>
  )
}
