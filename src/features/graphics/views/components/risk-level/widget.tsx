import { RiskLevelDescription, RiskLevelDescriptionProps } from './risk-level'
import { AreaChartProps } from '../area-chart'
import { DEFAULT_TIME_RANGE } from '../../../model/constants'
import { RangeItem } from '../../../model/types'
import { GraphicWrapper } from '../graphic-wrapper'
import { lazy, ReactNode, Suspense } from 'react'
import { TimeRangeProps } from '../time-range/time-range'

const LazyAreaChart = lazy(() =>
  import('../area-chart').then(module => ({ default: module.AreaChart })),
)

type RiskLevelChartWidgetProps = Partial<TimeRangeProps> & {
  chartProps: AreaChartProps.Props
  descriptionProps: RiskLevelDescriptionProps
  timeRange?: RangeItem[]
  isLoading?: boolean
  isFetching?: boolean
  infoClassName?: string
  bottomContent?: ReactNode
  className?: string
}

export const RiskLevelChartWidget: React.FC<RiskLevelChartWidgetProps> = ({
  chartProps,
  descriptionProps,
  timeRange = DEFAULT_TIME_RANGE,
  isLoading,
  isFetching,
  infoClassName,
  bottomContent,
  onTimeRangeChange,
  defaultItem,
  className,
}) => {
  return (
    <GraphicWrapper
      isLoading={isLoading}
      isFetching={isFetching}
      content={
        <Suspense>
          <LazyAreaChart {...chartProps} type='redToGreen' />
        </Suspense>
      }
      leftDescription={<RiskLevelDescription {...descriptionProps} />}
      timeRange={timeRange}
      defaultItem={defaultItem}
      infoClassName={infoClassName}
      bottomContent={bottomContent}
      onTimeRangeChange={onTimeRangeChange}
      className={className}
    />
  )
}
